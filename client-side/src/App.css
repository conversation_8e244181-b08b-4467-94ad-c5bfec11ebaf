@import"https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap" layer(base);

/*! tailwindcss v4.0.15 | MIT License | https://tailwindcss.com */
@layer theme {

  :root,
  :host {
    --color-red-100: oklch(.936 .032 17.717);
    --color-red-700: oklch(.505 .213 27.518);
    --color-orange-50: #fff6ed;
    --color-orange-100: #ffead5;
    --color-orange-400: #fd853a;
    --color-orange-500: #fb6514;
    --color-orange-600: #ec4a0a;
    --color-orange-700: #c4320a;
    --color-yellow-100: oklch(.973 .071 103.193);
    --color-yellow-600: oklch(.681 .162 75.834);
    --color-green-100: oklch(.962 .044 156.743);
    --color-green-600: oklch(.627 .194 149.214);
    --color-green-700: oklch(.527 .154 150.069);
    --color-cyan-100: oklch(.956 .045 203.388);
    --color-cyan-600: oklch(.609 .126 221.723);
    --color-blue-100: oklch(.932 .032 255.585);
    --color-blue-500: oklch(.623 .214 259.815);
    --color-blue-700: oklch(.488 .243 264.376);
    --color-purple-50: oklch(.977 .014 308.299);
    --color-purple-100: oklch(.946 .033 307.174);
    --color-purple-400: oklch(.714 .203 305.504);
    --color-purple-500: oklch(.627 .265 303.9);
    --color-purple-600: oklch(.558 .288 302.321);
    --color-purple-700: oklch(.496 .265 301.924);
    --color-pink-100: oklch(.948 .028 342.258);
    --color-pink-600: oklch(.592 .249 .584);
    --color-gray-50: #f9fafb;
    --color-gray-100: #f2f4f7;
    --color-gray-200: #e4e7ec;
    --color-gray-300: #d0d5dd;
    --color-gray-400: #98a2b3;
    --color-gray-500: #667085;
    --color-gray-600: #475467;
    --color-gray-700: #344054;
    --color-gray-800: #1d2939;
    --color-gray-900: #101828;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1/.75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25/.875);
    --text-base: 1rem;
    --text-base--line-height: 1.5;
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75/1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75/1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2/1.5);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, .2, 1)infinite;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --font-outfit: Outfit, sans-serif;
    --breakpoint-2xl: 1536px;
    --text-title-2xl: 72px;
    --text-title-2xl--line-height: 90px;
    --text-title-xl: 60px;
    --text-title-xl--line-height: 72px;
    --text-title-lg: 48px;
    --text-title-lg--line-height: 60px;
    --text-title-md: 36px;
    --text-title-md--line-height: 44px;
    --text-title-sm: 30px;
    --text-title-sm--line-height: 38px;
    --text-theme-xl: 20px;
    --text-theme-xl--line-height: 30px;
    --text-theme-sm: 14px;
    --text-theme-sm--line-height: 20px;
    --text-theme-xs: 12px;
    --text-theme-xs--line-height: 18px;
    --color-brand-50: #ecf3ff;
    --color-brand-100: #dde9ff;
    --color-brand-200: #c2d6ff;
    --color-brand-300: #9cb9ff;
    --color-brand-400: #7592ff;
    --color-brand-500: #465fff;
    --color-brand-600: #3641f5;
    --color-brand-700: #2a31d8;
    --color-brand-800: #252dae;
    --color-brand-950: #161950;
    --color-blue-light-50: #f0f9ff;
    --color-blue-light-500: #0ba5ec;
    --color-blue-light-600: #0086c9;
    --color-gray-dark: #1a2231;
    --color-success-50: #ecfdf3;
    --color-success-300: #6ce9a6;
    --color-success-400: #32d583;
    --color-success-500: #12b76a;
    --color-success-600: #039855;
    --color-success-700: #027a48;
    --color-success-800: #05603a;
    --color-error-50: #fef3f2;
    --color-error-100: #fee4e2;
    --color-error-300: #fda29b;
    --color-error-400: #f97066;
    --color-error-500: #f04438;
    --color-error-600: #d92d20;
    --color-error-700: #b42318;
    --color-error-800: #912018;
    --color-warning-50: #fffaeb;
    --color-warning-500: #f79009;
    --color-warning-600: #dc6803;
    --color-warning-700: #b54708;
    --color-theme-pink-500: #ee46bc;
    --color-theme-purple-500: #7a5af8;
    --drop-shadow-4xl: 0 35px 35px #00000040, 0 45px 65px #00000026;
    --z-index-1: 1;
    --z-index-999: 999;
    --z-index-99999: 99999;
    --z-index-999999: 999999
  }
}

@layer base {

  *,
  :after,
  :before,
  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0
  }

  html,
  : host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
  }

  abbr: where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit
  }

  b,
  strong {
    font-weight: bolder
  }

  code,
  kbd,
  samp,
  pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em
  }

  small {
    font-size: 80%
  }

  sub,
  sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative
  }

  sub {
    bottom: -.25em
  }

  sup {
    top: -.5em
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
  }

  :-moz-focusring {
    outline: auto
  }

  progress {
    vertical-align: baseline
  }

  summary {
    display: list-item
  }

  ol,
  ul,
  menu {
    list-style: none
  }

  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    vertical-align: middle;
    display: block
  }

  img,
  video {
    max-width: 100%;
    height: auto
  }

  button,
  input,
  select,
  optgroup,
  textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0
  }

  :where(select: is([multiple], [size])) optgroup {
    font-weight: bolder
  }

  :where(select: is([multiple], [size])) optgroup option {
    padding-inline-start: 20px
  }

  ::file-selector-button {
    margin-inline-end: 4px
  }

  ::placeholder {
    opacity: 1
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size:1px) {
    ::placeholder {
      color: color-mix(in oklab, currentColor 50%, transparent)
    }
  }

  textarea {
    resize: vertical
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit
  }

  ::-webkit-datetime-edit {
    display: inline-flex
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0
  }

  ::-webkit-datetime-edit {
    padding-block: 0
  }

  ::-webkit-datetime-edit-year-field {
    padding-block: 0
  }

  ::-webkit-datetime-edit-month-field {
    padding-block: 0
  }

  ::-webkit-datetime-edit-day-field {
    padding-block: 0
  }

  ::-webkit-datetime-edit-hour-field {
    padding-block: 0
  }

  ::-webkit-datetime-edit-minute-field {
    padding-block: 0
  }

  ::-webkit-datetime-edit-second-field {
    padding-block: 0
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-block: 0
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0
  }

  :-moz-ui-invalid {
    box-shadow: none
  }

  button,
  input: where([type=button], [type=reset], [type=submit]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button
  }

  ::-webkit-inner-spin-button {
    height: auto
  }

  ::-webkit-outer-spin-button {
    height: auto
  }

  [hidden]: where(:not([hidden=until-found])) {
    display: none !important
  }

  *,
  :after,
  :before,
  ::backdrop {
    border-color: var(--color-gray-200, currentColor)
  }

  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor)
  }

  button: not(:disabled),
  [role=button]:not(:disabled) {
    cursor: pointer
  }

  body {
    z-index: var(--z-index-1);
    background-color: var(--color-gray-50);
    font-family: var(--font-outfit);
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
    position: relative
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none
  }

  .invisible {
    visibility: hidden
  }

  .visible {
    visibility: visible
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden
  }

  .menu-item {
    align-items: center;
    gap: calc(var(--spacing)*3);
    border-radius: var(--radius-lg);
    width: 100%;
    padding-inline: calc(var(--spacing)*3);
    padding-block: calc(var(--spacing)*2);
    font-size: var(--text-theme-sm);
    line-height: var(--tw-leading, var(--text-theme-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    display: flex;
    position: relative
  }

  .menu-dropdown-item {
    align-items: center;
    gap: calc(var(--spacing)*3);
    border-radius: var(--radius-lg);
    padding-inline: calc(var(--spacing)*3);
    padding-block: calc(var(--spacing)*2.5);
    font-size: var(--text-theme-sm);
    line-height: var(--tw-leading, var(--text-theme-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    display: flex;
    position: relative
  }

  .absolute {
    position: absolute
  }

  .fixed {
    position: fixed
  }

  .relative {
    position: relative
  }

  .static {
    position: static
  }

  .sticky {
    position: sticky
  }

  .inset-0 {
    inset: calc(var(--spacing)*0)
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing)*0)
  }

  .-top-1 {
    top: calc(var(--spacing)*-1)
  }

  .-top-2 {
    top: calc(var(--spacing)*-2)
  }

  .-top-7 {
    top: calc(var(--spacing)*-7)
  }

  .top-0 {
    top: calc(var(--spacing)*0)
  }

  .top-0\.5 {
    top: calc(var(--spacing)*.5)
  }

  .top-1\/2 {
    top: 50%
  }

  .top-3 {
    top: calc(var(--spacing)*3)
  }

  .top-4 {
    top: calc(var(--spacing)*4)
  }

  .top-5 {
    top: calc(var(--spacing)*5)
  }

  .top-6 {
    top: calc(var(--spacing)*6)
  }

  .top-\[60\%\] {
    top: 60%
  }

  .top-full {
    top: 100%
  }

  .-right-1\.5 {
    right: calc(var(--spacing)*-1.5)
  }

  .-right-2 {
    right: calc(var(--spacing)*-2)
  }

  .-right-\[240px\] {
    right: -240px
  }

  .right-0 {
    right: calc(var(--spacing)*0)
  }

  .right-2 {
    right: calc(var(--spacing)*2)
  }

  .right-2\.5 {
    right: calc(var(--spacing)*2.5)
  }

  .right-3 {
    right: calc(var(--spacing)*3)
  }

  .right-3\.5 {
    right: calc(var(--spacing)*3.5)
  }

  .right-4 {
    right: calc(var(--spacing)*4)
  }

  .right-5 {
    right: calc(var(--spacing)*5)
  }

  .right-6 {
    right: calc(var(--spacing)*6)
  }

  .right-full {
    right: 100%
  }

  .-bottom-1 {
    bottom: calc(var(--spacing)*-1)
  }

  .bottom-0 {
    bottom: calc(var(--spacing)*0)
  }

  .bottom-6 {
    bottom: calc(var(--spacing)*6)
  }

  .bottom-10 {
    bottom: calc(var(--spacing)*10)
  }

  .bottom-full {
    bottom: 100%
  }

  .-left-1\.5 {
    left: calc(var(--spacing)*-1.5)
  }

  .-left-9 {
    left: calc(var(--spacing)*-9)
  }

  .-left-px {
    left: -1px
  }

  .left-0 {
    left: calc(var(--spacing)*0)
  }

  .left-0\.5 {
    left: calc(var(--spacing)*.5)
  }

  .left-1 {
    left: calc(var(--spacing)*1)
  }

  .left-1\/2 {
    left: 50%
  }

  .left-4 {
    left: calc(var(--spacing)*4)
  }

  .left-5 {
    left: calc(var(--spacing)*5)
  }

  .left-full {
    left: 100%
  }

  .-z-1 {
    z-index: calc(var(--z-index-1)*-1)
  }

  .z-1 {
    z-index: var(--z-index-1)
  }

  .z-10 {
    z-index: 10
  }

  .z-20 {
    z-index: 20
  }

  .z-30 {
    z-index: 30
  }

  .z-40 {
    z-index: 40
  }

  .z-50 {
    z-index: 50
  }

  .z-999 {
    z-index: var(--z-index-999)
  }

  .z-99999 {
    z-index: var(--z-index-99999)
  }

  .z-999999 {
    z-index: var(--z-index-999999)
  }

  .order-2 {
    order: 2
  }

  .order-3 {
    order: 3
  }

  .col-span-1 {
    grid-column: span 1/span 1
  }

  .col-span-2 {
    grid-column: span 2/span 2
  }

  .col-span-12 {
    grid-column: span 12/span 12
  }

  .col-span-full {
    grid-column: 1/-1
  }

  .container {
    width: 100%
  }

  @media (width>=375px) {
    .container {
      max-width: 375px
    }
  }

  @media (width>=425px) {
    .container {
      max-width: 425px
    }
  }

  @media (width>=640px) {
    .container {
      max-width: 640px
    }
  }

  @media (width>=768px) {
    .container {
      max-width: 768px
    }
  }

  @media (width>=1024px) {
    .container {
      max-width: 1024px
    }
  }

  @media (width>=1280px) {
    .container {
      max-width: 1280px
    }
  }

  @media (width>=1536px) {
    .container {
      max-width: 1536px
    }
  }

  @media (width>=2000px) {
    .container {
      max-width: 2000px
    }
  }

  .m-0 {
    margin: calc(var(--spacing)*0)
  }

  .m-0\! {
    margin: calc(var(--spacing)*0) !important
  }

  .m-4 {
    margin: calc(var(--spacing)*4)
  }

  .-mx-4 {
    margin-inline: calc(var(--spacing)*-4)
  }

  .mx-2 {
    margin-inline: calc(var(--spacing)*2)
  }

  .mx-auto {
    margin-inline: auto
  }

  .-my-6 {
    margin-block: calc(var(--spacing)*-6)
  }

  .my-1\.5 {
    margin-block: calc(var(--spacing)*1.5)
  }

  .my-2 {
    margin-block: calc(var(--spacing)*2)
  }

  .my-4 {
    margin-block: calc(var(--spacing)*4)
  }

  .my-5 {
    margin-block: calc(var(--spacing)*5)
  }

  .my-6 {
    margin-block: calc(var(--spacing)*6)
  }

  .-mt-0\.5 {
    margin-top: calc(var(--spacing)*-.5)
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing)*.5)
  }

  .mt-1 {
    margin-top: calc(var(--spacing)*1)
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing)*1.5)
  }

  .mt-2 {
    margin-top: calc(var(--spacing)*2)
  }

  .mt-2\.5 {
    margin-top: calc(var(--spacing)*2.5)
  }

  .mt-3 {
    margin-top: calc(var(--spacing)*3)
  }

  .mt-4 {
    margin-top: calc(var(--spacing)*4)
  }

  .mt-5 {
    margin-top: calc(var(--spacing)*5)
  }

  .mt-6 {
    margin-top: calc(var(--spacing)*6)
  }

  .mt-7 {
    margin-top: calc(var(--spacing)*7)
  }

  .mt-8 {
    margin-top: calc(var(--spacing)*8)
  }

  .mt-10 {
    margin-top: calc(var(--spacing)*10)
  }

  .mt-16 {
    margin-top: calc(var(--spacing)*16)
  }

  .mt-20 {
    margin-top: calc(var(--spacing)*20)
  }

  .mt-\[17px\] {
    margin-top: 17px
  }

  .-mr-2\.5 {
    margin-right: calc(var(--spacing)*-2.5)
  }

  .mr-1 {
    margin-right: calc(var(--spacing)*1)
  }

  .mr-2 {
    margin-right: calc(var(--spacing)*2)
  }

  .mr-2\.5 {
    margin-right: calc(var(--spacing)*2.5)
  }

  .mr-3 {
    margin-right: calc(var(--spacing)*3)
  }

  .mr-10 {
    margin-right: calc(var(--spacing)*10)
  }

  .-mb-1\.5 {
    margin-bottom: calc(var(--spacing)*-1.5)
  }

  .-mb-4 {
    margin-bottom: calc(var(--spacing)*-4)
  }

  .-mb-px {
    margin-bottom: -1px
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing)*0)
  }

  .mb-0\.5 {
    margin-bottom: calc(var(--spacing)*.5)
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing)*1)
  }

  .mb-1\.5 {
    margin-bottom: calc(var(--spacing)*1.5)
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing)*2)
  }

  .mb-2\.5 {
    margin-bottom: calc(var(--spacing)*2.5)
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing)*3)
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing)*4)
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing)*5)
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing)*6)
  }

  .mb-7 {
    margin-bottom: calc(var(--spacing)*7)
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing)*8)
  }

  .mb-9 {
    margin-bottom: calc(var(--spacing)*9)
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing)*10)
  }

  .mb-\[22px\] {
    margin-bottom: 22px
  }

  .-ml-3 {
    margin-left: calc(var(--spacing)*-3)
  }

  .-ml-4 {
    margin-left: calc(var(--spacing)*-4)
  }

  .-ml-5 {
    margin-left: calc(var(--spacing)*-5)
  }

  .-ml-\[22px\] {
    margin-left: -22px
  }

  .-ml-px {
    margin-left: -1px
  }

  .ml-0 {
    margin-left: calc(var(--spacing)*0)
  }

  .ml-1 {
    margin-left: calc(var(--spacing)*1)
  }

  .ml-2 {
    margin-left: calc(var(--spacing)*2)
  }

  .ml-2\.5 {
    margin-left: calc(var(--spacing)*2.5)
  }

  .ml-3 {
    margin-left: calc(var(--spacing)*3)
  }

  .ml-4 {
    margin-left: calc(var(--spacing)*4)
  }

  .ml-9 {
    margin-left: calc(var(--spacing)*9)
  }

  .ml-auto {
    margin-left: auto
  }

  .menu-dropdown-badge {
    padding-inline: calc(var(--spacing)*2.5);
    padding-block: calc(var(--spacing)*.5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--color-brand-500);
    text-transform: uppercase;
    border-radius: 3.40282e38px;
    display: block
  }

  .menu-dropdown-badge:is(.dark *) {
    color: var(--color-brand-400)
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none
  }

  .block {
    display: block
  }

  .flex {
    display: flex
  }

  .grid {
    display: grid
  }

  .hidden {
    display: none
  }

  .inline {
    display: inline
  }

  .inline-block {
    display: inline-block
  }

  .inline-flex {
    display: inline-flex
  }

  .table {
    display: table
  }

  .aspect-4\/3 {
    aspect-ratio: 4/3
  }

  .aspect-21\/9 {
    aspect-ratio: 21/9
  }

  .aspect-square {
    aspect-ratio: 1
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: calc(var(--spacing)*1.5);
    height: calc(var(--spacing)*1.5)
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    border-radius: 3.40282e38px
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: var(--color-gray-200);
    border-radius: 3.40282e38px
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:is() {
    background-color: var(--color-gray-700)
  }

  .menu-item-icon-size svg {
    width: calc(var(--spacing)*6) !important;
    height: calc(var(--spacing)*6) !important
  }

  .size-5 {
    width: calc(var(--spacing)*5);
    height: calc(var(--spacing)*5)
  }

  .size-6 {
    width: calc(var(--spacing)*6);
    height: calc(var(--spacing)*6)
  }

  .size-8 {
    width: calc(var(--spacing)*8);
    height: calc(var(--spacing)*8)
  }

  .size-10 {
    width: calc(var(--spacing)*10);
    height: calc(var(--spacing)*10)
  }

  .size-12 {
    width: calc(var(--spacing)*12);
    height: calc(var(--spacing)*12)
  }

  .size-14 {
    width: calc(var(--spacing)*14);
    height: calc(var(--spacing)*14)
  }

  .h-1 {
    height: calc(var(--spacing)*1)
  }

  .h-1\.5 {
    height: calc(var(--spacing)*1.5)
  }

  .h-2 {
    height: calc(var(--spacing)*2)
  }

  .h-2\.5 {
    height: calc(var(--spacing)*2.5)
  }

  .h-3 {
    height: calc(var(--spacing)*3)
  }

  .h-3\.5 {
    height: calc(var(--spacing)*3.5)
  }

  .h-4 {
    height: calc(var(--spacing)*4)
  }

  .h-5 {
    height: calc(var(--spacing)*5)
  }

  .h-6 {
    height: calc(var(--spacing)*6)
  }

  .h-7 {
    height: calc(var(--spacing)*7)
  }

  .h-8 {
    height: calc(var(--spacing)*8)
  }

  .h-9 {
    height: calc(var(--spacing)*9)
  }

  .h-9\.5 {
    height: calc(var(--spacing)*9.5)
  }

  .h-10 {
    height: calc(var(--spacing)*10)
  }

  .h-11 {
    height: calc(var(--spacing)*11)
  }

  .h-12 {
    height: calc(var(--spacing)*12)
  }

  .h-14 {
    height: calc(var(--spacing)*14)
  }

  .h-16 {
    height: calc(var(--spacing)*16)
  }

  .h-20 {
    height: calc(var(--spacing)*20)
  }

  .h-\[1\.5px\] {
    height: 1.5px
  }

  .h-\[3px\] {
    height: 3px
  }

  .h-\[42px\] {
    height: 42px
  }

  .h-\[50px\] {
    height: 50px
  }

  .h-\[52px\] {
    height: 52px
  }

  .h-\[56px\] {
    height: 56px
  }

  .h-\[60px\] {
    height: 60px
  }

  .h-\[68px\] {
    height: 68px
  }

  .h-\[212px\] {
    height: 212px
  }

  .h-\[372px\] {
    height: 372px
  }

  .h-\[450px\] {
    height: 450px
  }

  .h-\[480px\] {
    height: 480px
  }

  .h-\[calc\(100vh-150px\)\] {
    height: calc(100vh - 150px)
  }

  .h-auto {
    height: auto
  }

  .h-full {
    height: 100%
  }

  .h-px {
    height: 1px
  }

  .h-screen {
    height: 100vh
  }

  .max-h-\[330px\] {
    max-height: 330px
  }

  .max-h-\[500px\] {
    max-height: 500px
  }

  .max-h-\[510px\] {
    max-height: 510px
  }

  .max-h-\[550px\] {
    max-height: 550px
  }

  .max-h-full {
    max-height: 100%
  }

  .min-h-\[155px\] {
    min-height: 155px
  }

  .min-h-screen {
    min-height: 100vh
  }

  .w-1 {
    width: calc(var(--spacing)*1)
  }

  .w-1\.5 {
    width: calc(var(--spacing)*1.5)
  }

  .w-1\/5 {
    width: 20%
  }

  .w-2 {
    width: calc(var(--spacing)*2)
  }

  .w-2\.5 {
    width: calc(var(--spacing)*2.5)
  }

  .w-3 {
    width: calc(var(--spacing)*3)
  }

  .w-3\.5 {
    width: calc(var(--spacing)*3.5)
  }

  .w-3\/5 {
    width: 60%
  }

  .w-4 {
    width: calc(var(--spacing)*4)
  }

  .w-5 {
    width: calc(var(--spacing)*5)
  }

  .w-6 {
    width: calc(var(--spacing)*6)
  }

  .w-7 {
    width: calc(var(--spacing)*7)
  }

  .w-8 {
    width: calc(var(--spacing)*8)
  }

  .w-9 {
    width: calc(var(--spacing)*9)
  }

  .w-9\.5 {
    width: calc(var(--spacing)*9.5)
  }

  .w-10 {
    width: calc(var(--spacing)*10)
  }

  .w-11 {
    width: calc(var(--spacing)*11)
  }

  .w-12 {
    width: calc(var(--spacing)*12)
  }

  .w-14 {
    width: calc(var(--spacing)*14)
  }

  .w-16 {
    width: calc(var(--spacing)*16)
  }

  .w-20 {
    width: calc(var(--spacing)*20)
  }

  .w-24 {
    width: calc(var(--spacing)*24)
  }

  .w-40 {
    width: calc(var(--spacing)*40)
  }

  .w-\[3px\] {
    width: 3px
  }

  .w-\[23\%\] {
    width: 23%
  }

  .w-\[30\%\] {
    width: 30%
  }

  .w-\[40\%\] {
    width: 40%
  }

  .w-\[46px\] {
    width: 46px
  }

  .w-\[48\%\] {
    width: 48%
  }

  .w-\[50px\] {
    width: 50px
  }

  .w-\[52px\] {
    width: 52px
  }

  .w-\[55\%\] {
    width: 55%
  }

  .w-\[56px\] {
    width: 56px
  }

  .w-\[68px\] {
    width: 68px
  }

  .w-\[70\%\] {
    width: 70%
  }

  .w-\[79\%\] {
    width: 79%
  }

  .w-\[85\%\] {
    width: 85%
  }

  .w-\[90px\] {
    width: 90px
  }

  .w-\[120px\] {
    width: 120px
  }

  .w-\[140px\] {
    width: 140px
  }

  .w-\[252px\] {
    width: 252px
  }

  .w-\[260px\] {
    width: 260px
  }

  .w-\[290px\] {
    width: 290px
  }

  .w-\[300px\] {
    width: 300px
  }

  .w-\[350px\] {
    width: 350px
  }

  .w-auto {
    width: auto
  }

  .w-full {
    width: 100%
  }

  .w-px {
    width: 1px
  }

  .max-w-\(--breakpoint-2xl\) {
    max-width: var(--breakpoint-2xl)
  }

  .max-w-1\.5 {
    max-width: calc(var(--spacing)*1.5)
  }

  .max-w-2 {
    max-width: calc(var(--spacing)*2)
  }

  .max-w-2\.5 {
    max-width: calc(var(--spacing)*2.5)
  }

  .max-w-3 {
    max-width: calc(var(--spacing)*3)
  }

  .max-w-3\.5 {
    max-width: calc(var(--spacing)*3.5)
  }

  .max-w-4 {
    max-width: calc(var(--spacing)*4)
  }

  .max-w-5 {
    max-width: calc(var(--spacing)*5)
  }

  .max-w-6 {
    max-width: calc(var(--spacing)*6)
  }

  .max-w-8 {
    max-width: calc(var(--spacing)*8)
  }

  .max-w-10 {
    max-width: calc(var(--spacing)*10)
  }

  .max-w-11 {
    max-width: calc(var(--spacing)*11)
  }

  .max-w-12 {
    max-width: calc(var(--spacing)*12)
  }

  .max-w-14 {
    max-width: calc(var(--spacing)*14)
  }

  .max-w-16 {
    max-width: calc(var(--spacing)*16)
  }

  .max-w-60 {
    max-width: calc(var(--spacing)*60)
  }

  .max-w-\[48px\] {
    max-width: 48px
  }

  .max-w-\[100px\] {
    max-width: 100px
  }

  .max-w-\[140px\] {
    max-width: 140px
  }

  .max-w-\[150px\] {
    max-width: 150px
  }

  .max-w-\[155px\] {
    max-width: 155px
  }

  .max-w-\[242px\] {
    max-width: 242px
  }

  .max-w-\[250px\] {
    max-width: 250px
  }

  .max-w-\[270px\] {
    max-width: 270px
  }

  .max-w-\[274px\] {
    max-width: 274px
  }

  .max-w-\[290px\] {
    max-width: 290px
  }

  .max-w-\[380px\] {
    max-width: 380px
  }

  .max-w-\[385px\] {
    max-width: 385px
  }

  .max-w-\[460px\] {
    max-width: 460px
  }

  .max-w-\[507px\] {
    max-width: 507px
  }

  .max-w-\[577px\] {
    max-width: 577px
  }

  .max-w-\[584px\] {
    max-width: 584px
  }

  .max-w-\[600px\] {
    max-width: 600px
  }

  .max-w-\[607px\] {
    max-width: 607px
  }

  .max-w-\[630px\] {
    max-width: 630px
  }

  .max-w-\[700px\] {
    max-width: 700px
  }

  .max-w-full {
    max-width: 100%
  }

  .max-w-md {
    max-width: var(--container-md)
  }

  .max-w-xs {
    max-width: var(--container-xs)
  }

  .min-w-\[260px\] {
    min-width: 260px
  }

  .min-w-\[309px\] {
    min-width: 309px
  }

  .min-w-\[393px\] {
    min-width: 393px
  }

  .min-w-\[500px\] {
    min-width: 500px
  }

  .min-w-\[617px\] {
    min-width: 617px
  }

  .min-w-\[650px\] {
    min-width: 650px
  }

  .min-w-\[700px\] {
    min-width: 700px
  }

  .min-w-\[750px\] {
    min-width: 750px
  }

  .min-w-\[900px\] {
    min-width: 900px
  }

  .min-w-\[1000px\] {
    min-width: 1000px
  }

  .min-w-\[1300px\] {
    min-width: 1300px
  }

  .min-w-full {
    min-width: 100%
  }

  .flex-1 {
    flex: 1
  }

  .flex-auto {
    flex: auto
  }

  .flex-initial {
    flex: 0 auto
  }

  .flex-shrink-0,
  .shrink-0 {
    flex-shrink: 0
  }

  .grow {
    flex-grow: 1
  }

  .table-auto {
    table-layout: auto
  }

  .border-collapse {
    border-collapse: collapse
  }

  .-translate-x-1\/2 {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .-translate-x-\[55px\] {
    --tw-translate-x: -55px;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing)*0);
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .translate-x-1\/2 {
    --tw-translate-x: 50%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .-translate-y-1\/2 {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .-translate-y-\[60\%\] {
    --tw-translate-y: -60%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .-translate-y-\[95\%\] {
    --tw-translate-y: -95%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .translate-y-1\/2 {
    --tw-translate-y: 50%;
    translate: var(--tw-translate-x)var(--tw-translate-y)
  }

  .-rotate-45 {
    rotate: -45deg
  }

  .rotate-45 {
    rotate: 45deg
  }

  .rotate-180 {
    rotate: 180deg
  }

  .transform {
    transform: var(--tw-rotate-x)var(--tw-rotate-y)var(--tw-rotate-z)var(--tw-skew-x)var(--tw-skew-y)
  }

  .animate-ping {
    animation: var(--animate-ping)
  }

  .animate-spin {
    animation: var(--animate-spin)
  }

  .cursor-not-allowed {
    cursor: not-allowed
  }

  .cursor-pointer {
    cursor: pointer
  }

  .resize {
    resize: both
  }

  .list-decimal {
    list-style-type: decimal
  }

  .appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr))
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr))
  }

  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr))
  }

  .flex-col {
    flex-direction: column
  }

  .flex-col-reverse {
    flex-direction: column-reverse
  }

  .flex-row {
    flex-direction: row
  }

  .flex-row-reverse {
    flex-direction: row-reverse
  }

  .flex-wrap {
    flex-wrap: wrap
  }

  .items-baseline {
    align-items: baseline
  }

  .items-center {
    align-items: center
  }

  .items-end {
    align-items: flex-end
  }

  .items-start {
    align-items: flex-start
  }

  .justify-between {
    justify-content: space-between
  }

  .justify-center {
    justify-content: center
  }

  .justify-end {
    justify-content: flex-end
  }

  .justify-start {
    justify-content: flex-start
  }

  .gap-0\.5 {
    gap: calc(var(--spacing)*.5)
  }

  .gap-1 {
    gap: calc(var(--spacing)*1)
  }

  .gap-1\.5 {
    gap: calc(var(--spacing)*1.5)
  }

  .gap-2 {
    gap: calc(var(--spacing)*2)
  }

  .gap-2\.5 {
    gap: calc(var(--spacing)*2.5)
  }

  .gap-3 {
    gap: calc(var(--spacing)*3)
  }

  .gap-4 {
    gap: calc(var(--spacing)*4)
  }

  .gap-5 {
    gap: calc(var(--spacing)*5)
  }

  .gap-6 {
    gap: calc(var(--spacing)*6)
  }

  .gap-8 {
    gap: calc(var(--spacing)*8)
  }

  .gap-9 {
    gap: calc(var(--spacing)*9)
  }

  .gap-10 {
    gap: calc(var(--spacing)*10)
  }

  .gap-\[18px\] {
    gap: 18px
  }

  :where(.space-y-1>:not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))
  }

  :where(.space-y-3>:not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))
  }

  :where(.space-y-4>:not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))
  }

  :where(.space-y-5>:not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing)*5)*var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing)*5)*calc(1 - var(--tw-space-y-reverse)))
  }

  :where(.space-y-6>:not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))
  }

  :where(.space-y-8>:not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))
  }

  .gap-x-1 {
    column-gap: calc(var(--spacing)*1)
  }

  .gap-x-6 {
    column-gap: calc(var(--spacing)*6)
  }

  .gap-x-8 {
    column-gap: calc(var(--spacing)*8)
  }

  :where(.-space-x-2>:not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing)*-2)*var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing)*-2)*calc(1 - var(--tw-space-x-reverse)))
  }

  :where(.-space-x-3>:not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing)*-3)*var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing)*-3)*calc(1 - var(--tw-space-x-reverse)))
  }

  :where(.space-x-1>:not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing)*1)*var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-x-reverse)))
  }

  :where(.space-x-2>:not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))
  }

  :where(.space-x-3>:not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing)*3)*var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-x-reverse)))
  }

  .gap-y-2 {
    row-gap: calc(var(--spacing)*2)
  }

  .gap-y-5 {
    row-gap: calc(var(--spacing)*5)
  }

  :where(.divide-x>:not(:last-child)) {
    --tw-divide-x-reverse: 0;
    border-inline-style: var(--tw-border-style);
    border-inline-start-width: calc(1px*var(--tw-divide-x-reverse));
    border-inline-end-width: calc(1px*calc(1 - var(--tw-divide-x-reverse)))
  }

  : where(.divide-y>:not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px*var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px*calc(1 - var(--tw-divide-y-reverse)))
  }

  :where(.divide-gray-100>:not(:last-child)) {
    border-color: var(--color-gray-100)
  }

  :where(.divide-gray-200>:not(:last-child)) {
    border-color: var(--color-gray-200)
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
  }

  .overflow-auto {
    overflow: auto
  }

  .overflow-hidden {
    overflow: hidden
  }

  .overflow-x-auto {
    overflow-x: auto
  }

  .overflow-x-hidden {
    overflow-x: hidden
  }

  .overflow-y-auto {
    overflow-y: auto
  }

  .rounded {
    border-radius: .25rem
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl)
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl)
  }

  .rounded-\[10\.5px\] {
    border-radius: 10.5px
  }

  .rounded-full {
    border-radius: 3.40282e38px
  }

  .rounded-lg {
    border-radius: var(--radius-lg)
  }

  .rounded-md {
    border-radius: var(--radius-md)
  }

  .rounded-sm {
    border-radius: var(--radius-sm)
  }

  .rounded-xl {
    border-radius: var(--radius-xl)
  }

  .rounded-t {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
  }

  .rounded-t-xl {
    border-top-left-radius: var(--radius-xl);
    border-top-right-radius: var(--radius-xl)
  }

  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg)
  }

  .rounded-tl-sm {
    border-top-left-radius: var(--radius-sm)
  }

  .rounded-r-full {
    border-top-right-radius: 3.40282e38px;
    border-bottom-right-radius: 3.40282e38px
  }

  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg)
  }

  .rounded-tr-sm {
    border-top-right-radius: var(--radius-sm)
  }

  .rounded-b-2xl {
    border-bottom-right-radius: var(--radius-2xl);
    border-bottom-left-radius: var(--radius-2xl)
  }

  .rounded-b-xl {
    border-bottom-right-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl)
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px
  }

  .border-\[0\.5px\] {
    border-style: var(--tw-border-style);
    border-width: .5px
  }

  .border-\[0\.7px\] {
    border-style: var(--tw-border-style);
    border-width: .7px
  }

  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px
  }

  .border-\[1\.25px\] {
    border-style: var(--tw-border-style);
    border-width: 1.25px
  }

  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px
  }

  .border-t-0 {
    border-top-style: var(--tw-border-style);
    border-top-width: 0
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px
  }

  .border-b-0 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px
  }

  .border-b-4 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 4px
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed
  }

  .border-none {
    --tw-border-style: none;
    border-style: none
  }

  .border-blue-light-500 {
    border-color: var(--color-blue-light-500)
  }

  .border-brand-100 {
    border-color: var(--color-brand-100)
  }

  .border-brand-500 {
    border-color: var(--color-brand-500)
  }

  .border-error-500 {
    border-color: var(--color-error-500)
  }

  .border-gray-100 {
    border-color: var(--color-gray-100)
  }

  .border-gray-200 {
    border-color: var(--color-gray-200)
  }

  .border-gray-300 {
    border-color: var(--color-gray-300)
  }

  .border-gray-800 {
    border-color: var(--color-gray-800)
  }

  .border-success-500 {
    border-color: var(--color-success-500)
  }

  .border-transparent {
    border-color: #0000
  }

  .border-warning-500 {
    border-color: var(--color-warning-500)
  }

  .border-white {
    border-color: var(--color-white)
  }

  .menu-item-inactive {
    color: var(--color-gray-700)
  }

  @media (hover: hover) {
    .menu-item-inactive:is(:where(.group):hover *) {
      color: var(--color-gray-700)
    }

    .menu-item-inactive:hover {
      background-color: var(--color-gray-100)
    }
  }

  .menu-item-inactive:is(.dark *) {
    color: var(--color-gray-300)
  }

  @media (hover: hover) {
    .menu-item-inactive:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-white)5%, transparent);
      color: var(--color-gray-300)
    }
  }

  .menu-dropdown-item-active {
    background-color: var(--color-brand-50);
    color: var(--color-brand-500)
  }

  .menu-dropdown-item-active:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-500)12%, transparent);
    color: var(--color-brand-400)
  }

  .menu-dropdown-item-inactive {
    color: var(--color-gray-700)
  }

  @media (hover: hover) {
    .menu-dropdown-item-inactive:hover {
      background-color: var(--color-gray-100)
    }
  }

  .menu-dropdown-item-inactive:is(.dark *) {
    color: var(--color-gray-300)
  }

  @media (hover: hover) {
    .menu-dropdown-item-inactive:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-white)5%, transparent)
    }
  }

  .menu-item-inactive.active {
    background-color: var(--color-brand-50);
    color: var(--color-brand-500)
  }

  .menu-item-inactive.active:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-500)12%, transparent);
    color: var(--color-brand-400)
  }

  .menu-dropdown-badge-inactive {
    background-color: var(--color-brand-50)
  }

  @media (hover: hover) {
    .menu-dropdown-badge-inactive:is(:where(.group):hover *) {
      background-color: var(--color-brand-100)
    }
  }

  .menu-dropdown-badge-inactive:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-500)15%, transparent)
  }

  @media (hover: hover) {
    .menu-dropdown-badge-inactive:is(.dark *):is(:where(.group):hover *) {
      background-color: color-mix(in oklab, var(--color-brand-500)20%, transparent)
    }
  }

  .menu-dropdown-badge-active {
    background-color: var(--color-brand-100)
  }

  .menu-dropdown-badge-active:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-500)20%, transparent)
  }

  .bg-\[\#1E2634\] {
    background-color: #1e2634
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100)
  }

  .bg-blue-500\/\[0\.08\] {
    background-color: color-mix(in oklab, var(--color-blue-500)8%, transparent)
  }

  .bg-blue-light-50 {
    background-color: var(--color-blue-light-50)
  }

  .bg-blue-light-500 {
    background-color: var(--color-blue-light-500)
  }

  .bg-brand-50 {
    background-color: var(--color-brand-50)
  }

  .bg-brand-100 {
    background-color: var(--color-brand-100)
  }

  .bg-brand-300 {
    background-color: var(--color-brand-300)
  }

  .bg-brand-500 {
    background-color: var(--color-brand-500)
  }

  .bg-brand-950 {
    background-color: var(--color-brand-950)
  }

  .bg-cyan-100 {
    background-color: var(--color-cyan-100)
  }

  .bg-error-50 {
    background-color: var(--color-error-50)
  }

  .bg-error-100 {
    background-color: var(--color-error-100)
  }

  .bg-error-400 {
    background-color: var(--color-error-400)
  }

  .bg-error-500 {
    background-color: var(--color-error-500)
  }

  .bg-error-500\/10 {
    background-color: color-mix(in oklab, var(--color-error-500)10%, transparent)
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50)
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100)
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200)
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300)
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400)
  }

  .bg-gray-400\/50 {
    background-color: color-mix(in oklab, var(--color-gray-400)50%, transparent)
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500)
  }

  .bg-gray-700 {
    background-color: var(--color-gray-700)
  }

  .bg-gray-800 {
    background-color: var(--color-gray-800)
  }

  .bg-gray-900\/50 {
    background-color: color-mix(in oklab, var(--color-gray-900)50%, transparent)
  }

  .bg-green-100 {
    background-color: var(--color-green-100)
  }

  .bg-orange-50 {
    background-color: var(--color-orange-50)
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100)
  }

  .bg-orange-400 {
    background-color: var(--color-orange-400)
  }

  .bg-orange-500\/\[0\.08\] {
    background-color: color-mix(in oklab, var(--color-orange-500)8%, transparent)
  }

  .bg-pink-100 {
    background-color: var(--color-pink-100)
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50)
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100)
  }

  .bg-red-100 {
    background-color: var(--color-red-100)
  }

  .bg-success-50 {
    background-color: var(--color-success-50)
  }

  .bg-success-500 {
    background-color: var(--color-success-500)
  }

  .bg-success-500\/\[0\.08\] {
    background-color: color-mix(in oklab, var(--color-success-500)8%, transparent)
  }

  .bg-theme-pink-500\/\[0\.08\] {
    background-color: color-mix(in oklab, var(--color-theme-pink-500)8%, transparent)
  }

  .bg-theme-purple-500\/\[0\.08\] {
    background-color: color-mix(in oklab, var(--color-theme-purple-500)8%, transparent)
  }

  .bg-transparent {
    background-color: #0000
  }

  .bg-warning-50 {
    background-color: var(--color-warning-50)
  }

  .bg-warning-500 {
    background-color: var(--color-warning-500)
  }

  .bg-warning-500\/\[0\.08\] {
    background-color: color-mix(in oklab, var(--color-warning-500)8%, transparent)
  }

  .bg-white {
    background-color: var(--color-white)
  }

  .bg-white\/10 {
    background-color: color-mix(in oklab, var(--color-white)10%, transparent)
  }

  .bg-white\/20 {
    background-color: color-mix(in oklab, var(--color-white)20%, transparent)
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100)
  }

  .bg-none {
    background-image: none
  }

  .fill-blue-light-50 {
    fill: var(--color-blue-light-50)
  }

  .fill-blue-light-500 {
    fill: var(--color-blue-light-500)
  }

  .fill-current {
    fill: currentColor
  }

  .fill-error-50 {
    fill: var(--color-error-50)
  }

  .fill-error-600 {
    fill: var(--color-error-600)
  }

  .fill-gray-500 {
    fill: var(--color-gray-500)
  }

  .fill-gray-700 {
    fill: var(--color-gray-700)
  }

  .fill-gray-800 {
    fill: var(--color-gray-800)
  }

  .fill-success-50 {
    fill: var(--color-success-50)
  }

  .fill-success-600 {
    fill: var(--color-success-600)
  }

  .fill-warning-50 {
    fill: var(--color-warning-50)
  }

  .fill-warning-600 {
    fill: var(--color-warning-600)
  }

  .fill-white {
    fill: var(--color-white)
  }

  .stroke-brand-500 {
    stroke: var(--color-brand-500)
  }

  .stroke-current {
    stroke: currentColor
  }

  .stroke-gray-500 {
    stroke: var(--color-gray-500)
  }

  .object-cover {
    object-fit: cover
  }

  .object-center {
    object-position: center
  }

  .p-0\.5 {
    padding: calc(var(--spacing)*.5)
  }

  .p-1 {
    padding: calc(var(--spacing)*1)
  }

  .p-2 {
    padding: calc(var(--spacing)*2)
  }

  .p-2\.5 {
    padding: calc(var(--spacing)*2.5)
  }

  .p-3 {
    padding: calc(var(--spacing)*3)
  }

  .p-3\.5 {
    padding: calc(var(--spacing)*3.5)
  }

  .p-4 {
    padding: calc(var(--spacing)*4)
  }

  .p-5 {
    padding: calc(var(--spacing)*5)
  }

  .p-6 {
    padding: calc(var(--spacing)*6)
  }

  .p-7 {
    padding: calc(var(--spacing)*7)
  }

  .px-2 {
    padding-inline: calc(var(--spacing)*2)
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing)*2.5)
  }

  .px-3 {
    padding-inline: calc(var(--spacing)*3)
  }

  .px-3\.5 {
    padding-inline: calc(var(--spacing)*3.5)
  }

  .px-4 {
    padding-inline: calc(var(--spacing)*4)
  }

  .px-4\.5 {
    padding-inline: calc(var(--spacing)*4.5)
  }

  .px-5 {
    padding-inline: calc(var(--spacing)*5)
  }

  .px-6 {
    padding-inline: calc(var(--spacing)*6)
  }

  .px-7 {
    padding-inline: calc(var(--spacing)*7)
  }

  .px-\[7px\] {
    padding-inline: 7px
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing)*.5)
  }

  .py-1 {
    padding-block: calc(var(--spacing)*1)
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing)*1.5)
  }

  .py-2 {
    padding-block: calc(var(--spacing)*2)
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing)*2.5)
  }

  .py-3 {
    padding-block: calc(var(--spacing)*3)
  }

  .py-3\.5 {
    padding-block: calc(var(--spacing)*3.5)
  }

  .py-4 {
    padding-block: calc(var(--spacing)*4)
  }

  .py-5 {
    padding-block: calc(var(--spacing)*5)
  }

  .py-6 {
    padding-block: calc(var(--spacing)*6)
  }

  .py-7 {
    padding-block: calc(var(--spacing)*7)
  }

  .py-8 {
    padding-block: calc(var(--spacing)*8)
  }

  .py-\[4\.5px\] {
    padding-block: 4.5px
  }

  .py-\[18px\] {
    padding-block: 18px
  }

  .pt-3 {
    padding-top: calc(var(--spacing)*3)
  }

  .pt-4 {
    padding-top: calc(var(--spacing)*4)
  }

  .pt-5 {
    padding-top: calc(var(--spacing)*5)
  }

  .pt-6 {
    padding-top: calc(var(--spacing)*6)
  }

  .pt-10 {
    padding-top: calc(var(--spacing)*10)
  }

  .pt-16 {
    padding-top: calc(var(--spacing)*16)
  }

  .pr-1 {
    padding-right: calc(var(--spacing)*1)
  }

  .pr-2 {
    padding-right: calc(var(--spacing)*2)
  }

  .pr-2\.5 {
    padding-right: calc(var(--spacing)*2.5)
  }

  .pr-3 {
    padding-right: calc(var(--spacing)*3)
  }

  .pr-3\.5 {
    padding-right: calc(var(--spacing)*3.5)
  }

  .pr-4 {
    padding-right: calc(var(--spacing)*4)
  }

  .pr-5 {
    padding-right: calc(var(--spacing)*5)
  }

  .pr-8 {
    padding-right: calc(var(--spacing)*8)
  }

  .pr-11 {
    padding-right: calc(var(--spacing)*11)
  }

  .pr-14 {
    padding-right: calc(var(--spacing)*14)
  }

  .pr-\[84px\] {
    padding-right: 84px
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing)*1)
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing)*2)
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing)*3)
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing)*4)
  }

  .pb-5 {
    padding-bottom: calc(var(--spacing)*5)
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing)*6)
  }

  .pb-11 {
    padding-bottom: calc(var(--spacing)*11)
  }

  .pb-\[300px\] {
    padding-bottom: 300px
  }

  .pl-1 {
    padding-left: calc(var(--spacing)*1)
  }

  .pl-2 {
    padding-left: calc(var(--spacing)*2)
  }

  .pl-2\.5 {
    padding-left: calc(var(--spacing)*2.5)
  }

  .pl-3 {
    padding-left: calc(var(--spacing)*3)
  }

  .pl-3\.5 {
    padding-left: calc(var(--spacing)*3.5)
  }

  .pl-4 {
    padding-left: calc(var(--spacing)*4)
  }

  .pl-6 {
    padding-left: calc(var(--spacing)*6)
  }

  .pl-11 {
    padding-left: calc(var(--spacing)*11)
  }

  .pl-12 {
    padding-left: calc(var(--spacing)*12)
  }

  .pl-\[18px\] {
    padding-left: 18px
  }

  .pl-\[42px\] {
    padding-left: 42px
  }

  .pl-\[62px\] {
    padding-left: 62px
  }

  .pl-\[84px\] {
    padding-left: 84px
  }

  .text-center {
    text-align: center
  }

  .text-left {
    text-align: left
  }

  .text-right {
    text-align: right
  }

  .text-start {
    text-align: start
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height))
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height))
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height))
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height))
  }

  .text-theme-sm {
    font-size: var(--text-theme-sm);
    line-height: var(--tw-leading, var(--text-theme-sm--line-height))
  }

  .text-theme-xl {
    font-size: var(--text-theme-xl);
    line-height: var(--tw-leading, var(--text-theme-xl--line-height))
  }

  .text-theme-xs {
    font-size: var(--text-theme-xs);
    line-height: var(--tw-leading, var(--text-theme-xs--line-height))
  }

  .text-title-md {
    font-size: var(--text-title-md);
    line-height: var(--tw-leading, var(--text-title-md--line-height))
  }

  .text-title-sm {
    font-size: var(--text-title-sm);
    line-height: var(--tw-leading, var(--text-title-sm--line-height))
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height))
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height))
  }

  .text-\[10px\] {
    font-size: 10px
  }

  .leading-6 {
    --tw-leading: calc(var(--spacing)*6);
    line-height: calc(var(--spacing)*6)
  }

  .leading-\[18px\] {
    --tw-leading: 18px;
    line-height: 18px
  }

  .leading-\[20px\] {
    --tw-leading: 20px;
    line-height: 20px
  }

  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal)
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight)
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold)
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium)
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal)
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold)
  }

  .-tracking-\[0\.2px\] {
    --tw-tracking: -.2px;
    letter-spacing: -.2px
  }

  .whitespace-nowrap {
    white-space: nowrap
  }

  .menu-item-icon-inactive {
    color: var(--color-gray-500)
  }

  @media (hover: hover) {
    .menu-item-icon-inactive:is(:where(.group):hover *) {
      color: var(--color-gray-700)
    }
  }

  .menu-item-icon-inactive:is(.dark *) {
    color: var(--color-gray-400)
  }

  @media (hover: hover) {
    .menu-item-icon-inactive:is(.dark *):is(:where(.group):hover *) {
      color: var(--color-gray-300)
    }
  }

  .menu-item-icon-active {
    color: var(--color-brand-500)
  }

  .menu-item-icon-active:is(.dark *) {
    color: var(--color-brand-400)
  }

  .text-blue-700 {
    color: var(--color-blue-700)
  }

  .text-blue-light-500 {
    color: var(--color-blue-light-500)
  }

  .text-brand-500 {
    color: var(--color-brand-500)
  }

  .text-brand-600 {
    color: var(--color-brand-600)
  }

  .text-brand-700 {
    color: var(--color-brand-700)
  }

  .text-cyan-600 {
    color: var(--color-cyan-600)
  }

  .text-error-500 {
    color: var(--color-error-500)
  }

  .text-error-600 {
    color: var(--color-error-600)
  }

  .text-error-700 {
    color: var(--color-error-700)
  }

  .text-gray-200 {
    color: var(--color-gray-200)
  }

  .text-gray-300 {
    color: var(--color-gray-300)
  }

  .text-gray-400 {
    color: var(--color-gray-400)
  }

  .text-gray-500 {
    color: var(--color-gray-500)
  }

  .text-gray-500\/10 {
    color: color-mix(in oklab, var(--color-gray-500)10%, transparent)
  }

  .text-gray-500\/25 {
    color: color-mix(in oklab, var(--color-gray-500)25%, transparent)
  }

  .text-gray-500\/50 {
    color: color-mix(in oklab, var(--color-gray-500)50%, transparent)
  }

  .text-gray-500\/75 {
    color: color-mix(in oklab, var(--color-gray-500)75%, transparent)
  }

  .text-gray-600 {
    color: var(--color-gray-600)
  }

  .text-gray-700 {
    color: var(--color-gray-700)
  }

  .text-gray-800 {
    color: var(--color-gray-800)
  }

  .text-gray-900 {
    color: var(--color-gray-900)
  }

  .text-green-600 {
    color: var(--color-green-600)
  }

  .text-green-700 {
    color: var(--color-green-700)
  }

  .text-orange-500 {
    color: var(--color-orange-500)
  }

  .text-orange-600 {
    color: var(--color-orange-600)
  }

  .text-orange-700 {
    color: var(--color-orange-700)
  }

  .text-pink-600 {
    color: var(--color-pink-600)
  }

  .text-purple-600 {
    color: var(--color-purple-600)
  }

  .text-purple-700 {
    color: var(--color-purple-700)
  }

  .text-red-700 {
    color: var(--color-red-700)
  }

  .text-success-500 {
    color: var(--color-success-500)
  }

  .text-success-600 {
    color: var(--color-success-600)
  }

  .text-success-700 {
    color: var(--color-success-700)
  }

  .text-theme-pink-500 {
    color: var(--color-theme-pink-500)
  }

  .text-theme-purple-500 {
    color: var(--color-theme-purple-500)
  }

  .text-warning-500 {
    color: var(--color-warning-500)
  }

  .text-warning-600 {
    color: var(--color-warning-600)
  }

  .text-warning-700 {
    color: var(--color-warning-700)
  }

  .text-white {
    color: var(--color-white)
  }

  .text-white\/70 {
    color: color-mix(in oklab, var(--color-white)70%, transparent)
  }

  .text-white\/80 {
    color: color-mix(in oklab, var(--color-white)80%, transparent)
  }

  .text-white\/90 {
    color: color-mix(in oklab, var(--color-white)90%, transparent)
  }

  .text-yellow-600 {
    color: var(--color-yellow-600)
  }

  .capitalize {
    text-transform: capitalize
  }

  .uppercase {
    text-transform: uppercase
  }

  .line-through {
    text-decoration-line: line-through
  }

  .underline {
    text-decoration-line: underline
  }

  .opacity-0 {
    opacity: 0
  }

  .opacity-40 {
    opacity: .4
  }

  .opacity-50 {
    opacity: .5
  }

  .opacity-60 {
    opacity: .6
  }

  .opacity-75 {
    opacity: .75
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .shadow-theme-lg {
    --tw-shadow: 0px 12px 16px -4px var(--tw-shadow-color, #10182814), 0px 4px 6px -2px var(--tw-shadow-color, #10182808);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .shadow-theme-md {
    --tw-shadow: 0px 4px 8px -2px var(--tw-shadow-color, #1018281a), 0px 2px 4px -2px var(--tw-shadow-color, #1018280f);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .shadow-theme-sm {
    --tw-shadow: 0px 1px 3px 0px var(--tw-shadow-color, #1018281a), 0px 1px 2px 0px var(--tw-shadow-color, #1018280f);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .shadow-theme-xs {
    --tw-shadow: 0px 1px 2px 0px var(--tw-shadow-color, #1018280d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, )0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, )0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .ring-4 {
    --tw-ring-shadow: var(--tw-ring-inset, )0 0 0 calc(4px + var(--tw-ring-offset-width))var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .ring-brand-500 {
    --tw-ring-color: var(--color-brand-500)
  }

  .ring-gray-200 {
    --tw-ring-color: var(--color-gray-200)
  }

  .ring-gray-300 {
    --tw-ring-color: var(--color-gray-300)
  }

  .ring-white {
    --tw-ring-color: var(--color-white)
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none
  }

  @media (forced-colors:active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px
  }

  .drop-shadow-4xl {
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-4xl));
    filter: var(--tw-blur, )var(--tw-brightness, )var(--tw-contrast, )var(--tw-grayscale, )var(--tw-hue-rotate, )var(--tw-invert, )var(--tw-saturate, )var(--tw-sepia, )var(--tw-drop-shadow, )
  }

  .filter {
    filter: var(--tw-blur, )var(--tw-brightness, )var(--tw-contrast, )var(--tw-grayscale, )var(--tw-hue-rotate, )var(--tw-invert, )var(--tw-saturate, )var(--tw-sepia, )var(--tw-drop-shadow, )
  }

  .backdrop-blur-\[32px\] {
    --tw-backdrop-blur: blur(32px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, )var(--tw-backdrop-brightness, )var(--tw-backdrop-contrast, )var(--tw-backdrop-grayscale, )var(--tw-backdrop-hue-rotate, )var(--tw-backdrop-invert, )var(--tw-backdrop-opacity, )var(--tw-backdrop-saturate, )var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, )var(--tw-backdrop-brightness, )var(--tw-backdrop-contrast, )var(--tw-backdrop-grayscale, )var(--tw-backdrop-hue-rotate, )var(--tw-backdrop-invert, )var(--tw-backdrop-opacity, )var(--tw-backdrop-saturate, )var(--tw-backdrop-sepia, )
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration))
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration))
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration))
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration))
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration))
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out)
  }

  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none
  }

  .ring-inset {
    --tw-ring-inset: inset
  }

  @media (hover: hover) {
    .group-hover\:visible:is(:where(.group):hover *) {
      visibility: visible
    }

    .group-hover\:translate-x-0:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing)*0);
      translate: var(--tw-translate-x)var(--tw-translate-y)
    }

    .group-hover\:bg-brand-50:is(:where(.group):hover *) {
      background-color: var(--color-brand-50)
    }

    .group-hover\:fill-gray-700:is(:where(.group):hover *) {
      fill: var(--color-gray-700)
    }

    .group-hover\:fill-gray-800:is(:where(.group):hover *) {
      fill: var(--color-gray-800)
    }

    .group-hover\:text-brand-500:is(:where(.group):hover *) {
      color: var(--color-brand-500)
    }

    .group-hover\:text-gray-400:is(:where(.group):hover *) {
      color: var(--color-gray-400)
    }

    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1
    }
  }

  .file\: mr-5::file-selector-button {
    margin-right: calc(var(--spacing)*5)
  }

  .file\: border-collapse::file-selector-button {
    border-collapse: collapse
  }

  .file\: cursor-pointer::file-selector-button {
    cursor: pointer
  }

  .file\: rounded-l-lg::file-selector-button {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg)
  }

  .file\: border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0
  }

  .file\: border-r::file-selector-button {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px
  }

  .file\: border-solid::file-selector-button {
    --tw-border-style: solid;
    border-style: solid
  }

  .file\: border-gray-200::file-selector-button {
    border-color: var(--color-gray-200)
  }

  .file\: bg-gray-50::file-selector-button {
    background-color: var(--color-gray-50)
  }

  .file\: py-3::file-selector-button {
    padding-block: calc(var(--spacing)*3)
  }

  .file\: pr-3::file-selector-button {
    padding-right: calc(var(--spacing)*3)
  }

  .file\: pl-3\.5::file-selector-button {
    padding-left: calc(var(--spacing)*3.5)
  }

  .file\: text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height))
  }

  .file\: text-gray-700::file-selector-button {
    color: var(--color-gray-700)
  }

  .placeholder\: text-gray-400::placeholder {
    color: var(--color-gray-400)
  }

  .placeholder\: text-gray-800::placeholder {
    color: var(--color-gray-800)
  }

  .before\: absolute:before {
    content: var(--tw-content);
    position: absolute
  }

  .before\: top-0:before {
    content: var(--tw-content);
    top: calc(var(--spacing)*0)
  }

  .before\: -right-4:before {
    content: var(--tw-content);
    right: calc(var(--spacing)*-4)
  }

  .before\: border-\[13px\]:before {
    content: var(--tw-content);
    border-style: var(--tw-border-style);
    border-width: 13px
  }

  .before\: border-\[16px\]:before {
    content: var(--tw-content);
    border-style: var(--tw-border-style);
    border-width: 16px
  }

  .before\: border-transparent:before {
    content: var(--tw-content);
    border-color: #0000
  }

  .before\: border-t-brand-500:before {
    content: var(--tw-content);
    border-top-color: var(--color-brand-500)
  }

  .before\: border-l-brand-500:before {
    content: var(--tw-content);
    border-left-color: var(--color-brand-500)
  }

  .before\: content-\[\'\'\]:before {
    --tw-content: "";
    content: var(--tw-content)
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute
  }

  .after\:-right-4:after {
    content: var(--tw-content);
    right: calc(var(--spacing)*-4)
  }

  .after\:border-\[13px\]:after {
    content: var(--tw-content);
    border-style: var(--tw-border-style);
    border-width: 13px
  }

  .after\:border-\[16px\]:after {
    content: var(--tw-content);
    border-style: var(--tw-border-style);
    border-width: 16px
  }

  .after\:border-transparent:after {
    content: var(--tw-content);
    border-color: #0000
  }

  .after\:border-b-brand-500:after {
    content: var(--tw-content);
    border-bottom-color: var(--color-brand-500)
  }

  .after\:border-l-brand-500:after {
    content: var(--tw-content);
    border-left-color: var(--color-brand-500)
  }

  .after\:content-\[\'\'\]:after {
    --tw-content: "";
    content: var(--tw-content)
  }

  .first\:rounded-l-lg:first-child {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg)
  }

  .first\:pt-0:first-child {
    padding-top: calc(var(--spacing)*0)
  }

  .last\:rounded-r-lg:last-child {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg)
  }

  .last\:border-0:last-child {
    border-style: var(--tw-border-style);
    border-width: 0
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0
  }

  .last\:pb-0:last-child {
    padding-bottom: calc(var(--spacing)*0)
  }

  .checked\:border-transparent:checked {
    border-color: #0000
  }

  .checked\:bg-brand-500:checked {
    background-color: var(--color-brand-500)
  }

  @media (hover:hover) {
    .hover\:border-brand-500:hover {
      border-color: var(--color-brand-500)
    }

    .hover\:border-gray-200:hover {
      border-color: var(--color-gray-200)
    }

    .hover\:border-gray-300:hover {
      border-color: var(--color-gray-300)
    }

    .hover\:bg-blue-500\/\[0\.08\]:hover {
      background-color: color-mix(in oklab, var(--color-blue-500)8%, transparent)
    }

    .hover\:bg-blue-light-600:hover {
      background-color: var(--color-blue-light-600)
    }

    .hover\:bg-brand-50:hover {
      background-color: var(--color-brand-50)
    }

    .hover\:bg-brand-500:hover {
      background-color: var(--color-brand-500)
    }

    .hover\:bg-brand-500\/\[0\.08\]:hover {
      background-color: color-mix(in oklab, var(--color-brand-500)8%, transparent)
    }

    .hover\:bg-brand-600:hover {
      background-color: var(--color-brand-600)
    }

    .hover\:bg-error-600:hover {
      background-color: var(--color-error-600)
    }

    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50)
    }

    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100)
    }

    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200)
    }

    .hover\:bg-success-600:hover {
      background-color: var(--color-success-600)
    }

    .hover\:bg-warning-600:hover {
      background-color: var(--color-warning-600)
    }

    .hover\:text-brand-500:hover {
      color: var(--color-brand-500)
    }

    .hover\:text-brand-600:hover {
      color: var(--color-brand-600)
    }

    .hover\:text-error-500:hover {
      color: var(--color-error-500)
    }

    .hover\:text-gray-500\/10:hover {
      color: color-mix(in oklab, var(--color-gray-500)10%, transparent)
    }

    .hover\:text-gray-500\/25:hover {
      color: color-mix(in oklab, var(--color-gray-500)25%, transparent)
    }

    .hover\:text-gray-500\/50:hover {
      color: color-mix(in oklab, var(--color-gray-500)50%, transparent)
    }

    .hover\:text-gray-500\/75:hover {
      color: color-mix(in oklab, var(--color-gray-500)75%, transparent)
    }

    .hover\:text-gray-700:hover {
      color: var(--color-gray-700)
    }

    .hover\:text-gray-800:hover {
      color: var(--color-gray-800)
    }

    .hover\:text-gray-900:hover {
      color: var(--color-gray-900)
    }

    .hover\:text-white:hover {
      color: var(--color-white)
    }

    .hover\:file\:bg-gray-100:hover::file-selector-button {
      background-color: var(--color-gray-100)
    }
  }

  .focus\:border-0:focus {
    border-style: var(--tw-border-style);
    border-width: 0
  }

  .focus\:border-brand-300:focus {
    border-color: var(--color-brand-300)
  }

  .focus\:border-error-300:focus {
    border-color: var(--color-error-300)
  }

  .focus\:border-success-300:focus {
    border-color: var(--color-success-300)
  }

  .focus\:shadow-focus-ring:focus {
    --tw-shadow: 0px 0px 0px 4px var(--tw-shadow-color, #465fff1f);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .focus\:ring-0:focus {
    --tw-ring-shadow: var(--tw-ring-inset, )0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .focus\:ring-3:focus {
    --tw-ring-shadow: var(--tw-ring-inset, )0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
  }

  .focus\:ring-brand-500\/10:focus {
    --tw-ring-color: color-mix(in oklab, var(--color-brand-500)10%, transparent)
  }

  .focus\:ring-brand-500\/20:focus {
    --tw-ring-color: color-mix(in oklab, var(--color-brand-500)20%, transparent)
  }

  .focus\:ring-error-500\/10:focus {
    --tw-ring-color: color-mix(in oklab, var(--color-error-500)10%, transparent)
  }

  .focus\:ring-error-500\/20:focus {
    --tw-ring-color: color-mix(in oklab, var(--color-error-500)20%, transparent)
  }

  .focus\:ring-success-500\/20:focus {
    --tw-ring-color: color-mix(in oklab, var(--color-success-500)20%, transparent)
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none
  }

  @media (forced-colors:active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000
    }
  }

  .focus\:file\:ring-brand-300:focus::file-selector-button {
    --tw-ring-color: var(--color-brand-300)
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed
  }

  .disabled\:bg-brand-300:disabled {
    background-color: var(--color-brand-300)
  }

  .disabled\:opacity-50:disabled {
    opacity: .5
  }

  .disabled\:opacity-60:disabled {
    opacity: .6
  }

  @media (width>=375px) {
    .\32xsm\:w-\[307px\] {
      width: 307px
    }

    .\32xsm\:gap-3 {
      gap: calc(var(--spacing)*3)
    }
  }

  @media (width>=425px) {
    .xsm\:w-\[358px\] {
      width: 358px
    }

    .xsm\:pb-0 {
      padding-bottom: calc(var(--spacing)*0)
    }
  }

  @media (width>=640px) {
    .sm\:top-6 {
      top: calc(var(--spacing)*6)
    }

    .sm\:right-6 {
      right: calc(var(--spacing)*6)
    }

    .sm\:left-3 {
      left: calc(var(--spacing)*3)
    }

    .sm\:col-span-1 {
      grid-column: span 1/span 1
    }

    .sm\:col-span-2 {
      grid-column: span 2/span 2
    }

    .sm\:-mx-6 {
      margin-inline: calc(var(--spacing)*-6)
    }

    .sm\:mt-0 {
      margin-top: calc(var(--spacing)*0)
    }

    .sm\:mt-5 {
      margin-top: calc(var(--spacing)*5)
    }

    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing)*8)
    }

    .sm\:block {
      display: block
    }

    .sm\:flex {
      display: flex
    }

    .sm\:hidden {
      display: none
    }

    .sm\:inline {
      display: inline
    }

    .sm\:inline-block {
      display: inline-block
    }

    .sm\:h-11 {
      height: calc(var(--spacing)*11)
    }

    .sm\:h-\[158px\] {
      height: 158px
    }

    .sm\:h-\[calc\(100vh-174px\)\] {
      height: calc(100vh - 174px)
    }

    .sm\:w-11 {
      width: calc(var(--spacing)*11)
    }

    .sm\:w-\[60px\] {
      width: 60px
    }

    .sm\:w-\[200px\] {
      width: 200px
    }

    .sm\:w-\[228px\] {
      width: 228px
    }

    .sm\:w-\[320px\] {
      width: 320px
    }

    .sm\:w-\[361px\] {
      width: 361px
    }

    .sm\:w-auto {
      width: auto
    }

    .sm\:w-fit {
      width: fit-content
    }

    .sm\:w-px {
      width: 1px
    }

    .sm\:max-w-\[160px\] {
      max-width: 160px
    }

    .sm\:max-w-\[204px\] {
      max-width: 204px
    }

    .sm\:max-w-\[236px\] {
      max-width: 236px
    }

    .sm\:max-w-\[281px\] {
      max-width: 281px
    }

    .sm\:max-w-\[320px\] {
      max-width: 320px
    }

    .sm\:max-w-\[340px\] {
      max-width: 340px
    }

    .sm\:max-w-\[472px\] {
      max-width: 472px
    }

    .sm\:max-w-\[492px\] {
      max-width: 492px
    }

    .sm\:max-w-\[555px\] {
      max-width: 555px
    }

    .sm\:max-w-\[562px\] {
      max-width: 562px
    }

    .sm\:max-w-fit {
      max-width: fit-content
    }

    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .sm\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .sm\:flex-col {
      flex-direction: column
    }

    .sm\:flex-row {
      flex-direction: row
    }

    .sm\:items-center {
      align-items: center
    }

    .sm\:items-start {
      align-items: flex-start
    }

    .sm\:justify-between {
      justify-content: space-between
    }

    .sm\:justify-center {
      justify-content: center
    }

    .sm\:justify-end {
      justify-content: flex-end
    }

    .sm\:justify-normal {
      justify-content: normal
    }

    .sm\:gap-2 {
      gap: calc(var(--spacing)*2)
    }

    .sm\:gap-3 {
      gap: calc(var(--spacing)*3)
    }

    .sm\:gap-4 {
      gap: calc(var(--spacing)*4)
    }

    .sm\:gap-5 {
      gap: calc(var(--spacing)*5)
    }

    .sm\:gap-6 {
      gap: calc(var(--spacing)*6)
    }

    .sm\:gap-8 {
      gap: calc(var(--spacing)*8)
    }

    .sm\:gap-9 {
      gap: calc(var(--spacing)*9)
    }

    :where(.sm\:space-y-2>:not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))
    }

    :where(.sm\:space-y-5>:not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing)*5)*var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing)*5)*calc(1 - var(--tw-space-y-reverse)))
    }

    :where(.sm\:space-y-6>:not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))
    }

    .sm\:overflow-visible {
      overflow: visible
    }

    .sm\:border-t-0 {
      border-top-style: var(--tw-border-style);
      border-top-width: 0
    }

    .sm\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px
    }

    .sm\:border-b-0 {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0
    }

    .sm\:p-0 {
      padding: calc(var(--spacing)*0)
    }

    .sm\:p-2\.5 {
      padding: calc(var(--spacing)*2.5)
    }

    .sm\:p-3 {
      padding: calc(var(--spacing)*3)
    }

    .sm\:p-4 {
      padding: calc(var(--spacing)*4)
    }

    .sm\:p-5 {
      padding: calc(var(--spacing)*5)
    }

    .sm\:p-6 {
      padding: calc(var(--spacing)*6)
    }

    .sm\:px-3\.5 {
      padding-inline: calc(var(--spacing)*3.5)
    }

    .sm\:px-5 {
      padding-inline: calc(var(--spacing)*5)
    }

    .sm\:px-6 {
      padding-inline: calc(var(--spacing)*6)
    }

    .sm\:py-2 {
      padding-block: calc(var(--spacing)*2)
    }

    .sm\:py-2\.5 {
      padding-block: calc(var(--spacing)*2.5)
    }

    .sm\:py-5 {
      padding-block: calc(var(--spacing)*5)
    }

    .sm\:pt-5 {
      padding-top: calc(var(--spacing)*5)
    }

    .sm\:pt-6 {
      padding-top: calc(var(--spacing)*6)
    }

    .sm\:pt-10 {
      padding-top: calc(var(--spacing)*10)
    }

    .sm\:pr-4 {
      padding-right: calc(var(--spacing)*4)
    }

    .sm\:pb-0 {
      padding-bottom: calc(var(--spacing)*0)
    }

    .sm\:pl-6 {
      padding-left: calc(var(--spacing)*6)
    }

    .sm\:text-right {
      text-align: right
    }

    .sm\:text-start {
      text-align: start
    }

    .sm\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height))
    }

    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height))
    }

    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height))
    }

    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height))
    }

    .sm\:text-theme-xl {
      font-size: var(--text-theme-xl);
      line-height: var(--tw-leading, var(--text-theme-xl--line-height))
    }

    .sm\:text-title-md {
      font-size: var(--text-title-md);
      line-height: var(--tw-leading, var(--text-title-md--line-height))
    }

    .sm\:text-title-sm {
      font-size: var(--text-title-sm);
      line-height: var(--tw-leading, var(--text-title-sm--line-height))
    }
  }

  @media (width>=768px) {
    .md\:w-\[668px\] {
      width: 668px
    }

    .md\:flex-row {
      flex-direction: row
    }

    .md\:gap-6 {
      gap: calc(var(--spacing)*6)
    }

    .md\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px
    }

    .md\:border-b-0 {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0
    }

    .md\:p-6 {
      padding: calc(var(--spacing)*6)
    }
  }

  @media (width>=1024px) {
    .lg\:right-0 {
      right: calc(var(--spacing)*0)
    }

    .lg\:col-span-1 {
      grid-column: span 1/span 1
    }

    .lg\:mt-0 {
      margin-top: calc(var(--spacing)*0)
    }

    .lg\:mb-6 {
      margin-bottom: calc(var(--spacing)*6)
    }

    .lg\:mb-7 {
      margin-bottom: calc(var(--spacing)*7)
    }

    .lg\:ml-\[90px\] {
      margin-left: 90px
    }

    .lg\:ml-\[290px\] {
      margin-left: 290px
    }

    .lg\:block {
      display: block
    }

    .lg\:flex {
      display: flex
    }

    .lg\:grid {
      display: grid
    }

    .lg\:hidden {
      display: none
    }

    .lg\:inline-flex {
      display: inline-flex
    }

    .lg\:h-11 {
      height: calc(var(--spacing)*11)
    }

    .lg\:w-1\/2 {
      width: 50%
    }

    .lg\:w-11 {
      width: calc(var(--spacing)*11)
    }

    .lg\:w-\[634px\] {
      width: 634px
    }

    .lg\:w-auto {
      width: auto
    }

    .lg\:translate-x-0 {
      --tw-translate-x: calc(var(--spacing)*0);
      translate: var(--tw-translate-x)var(--tw-translate-y)
    }

    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .lg\:flex-row {
      flex-direction: row
    }

    .lg\:items-start {
      align-items: flex-start
    }

    .lg\:justify-between {
      justify-content: space-between
    }

    .lg\:justify-center {
      justify-content: center
    }

    .lg\:justify-end {
      justify-content: flex-end
    }

    .lg\:justify-normal {
      justify-content: normal
    }

    .lg\:justify-start {
      justify-content: flex-start
    }

    .lg\:gap-7 {
      gap: calc(var(--spacing)*7)
    }

    .lg\:border {
      border-style: var(--tw-border-style);
      border-width: 1px
    }

    .lg\:border-b {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px
    }

    .lg\:border-b-0 {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0
    }

    .lg\:p-6 {
      padding: calc(var(--spacing)*6)
    }

    .lg\:p-10 {
      padding: calc(var(--spacing)*10)
    }

    .lg\:p-11 {
      padding: calc(var(--spacing)*11)
    }

    .lg\:px-0 {
      padding-inline: calc(var(--spacing)*0)
    }

    .lg\:px-6 {
      padding-inline: calc(var(--spacing)*6)
    }

    .lg\:py-4 {
      padding-block: calc(var(--spacing)*4)
    }

    .lg\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height))
    }

    .lg\:shadow-none {
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
    }
  }

  @media (width>=1280px) {
    .xl\:order-2 {
      order: 2
    }

    .xl\:order-3 {
      order: 3
    }

    .xl\:col-span-3 {
      grid-column: span 3/span 3
    }

    .xl\:col-span-4 {
      grid-column: span 4/span 4
    }

    .xl\:col-span-5 {
      grid-column: span 5/span 5
    }

    .xl\:col-span-6 {
      grid-column: span 6/span 6
    }

    .xl\:col-span-7 {
      grid-column: span 7/span 7
    }

    .xl\:col-span-8 {
      grid-column: span 8/span 8
    }

    .xl\:col-span-9 {
      grid-column: span 9/span 9
    }

    .xl\:ml-5 {
      margin-left: calc(var(--spacing)*5)
    }

    .xl\:block {
      display: block
    }

    .xl\:flex {
      display: flex
    }

    .xl\:grid {
      display: grid
    }

    .xl\:hidden {
      display: none
    }

    .xl\:h-\[calc\(100vh-186px\)\] {
      height: calc(100vh - 186px)
    }

    .xl\:h-full {
      height: 100%
    }

    .xl\:w-1\/4 {
      width: 25%
    }

    .xl\:w-1\/5 {
      width: 20%
    }

    .xl\:w-3\/4 {
      width: 75%
    }

    .xl\:w-4\/5 {
      width: 80%
    }

    .xl\:w-\[300px\] {
      width: 300px
    }

    .xl\:w-\[393px\] {
      width: 393px
    }

    .xl\:w-\[430px\] {
      width: 430px
    }

    .xl\:w-auto {
      width: auto
    }

    .xl\:max-w-\[450px\] {
      max-width: 450px
    }

    .xl\:min-w-full {
      min-width: 100%
    }

    .xl\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr))
    }

    .xl\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr))
    }

    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .xl\:grid-cols-12 {
      grid-template-columns: repeat(12, minmax(0, 1fr))
    }

    .xl\:flex-col {
      flex-direction: column
    }

    .xl\:flex-row {
      flex-direction: row
    }

    .xl\:items-center {
      align-items: center
    }

    .xl\:justify-between {
      justify-content: space-between
    }

    .xl\:justify-end {
      justify-content: flex-end
    }

    .xl\:justify-normal {
      justify-content: normal
    }

    .xl\:justify-start {
      justify-content: flex-start
    }

    .xl\:gap-3 {
      gap: calc(var(--spacing)*3)
    }

    .xl\:gap-5 {
      gap: calc(var(--spacing)*5)
    }

    .xl\:gap-6 {
      gap: calc(var(--spacing)*6)
    }

    :where(.xl\:space-y-0>:not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))
    }

    :where(.xl\:space-y-8>:not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))
    }

    .xl\:border-t-0 {
      border-top-style: var(--tw-border-style);
      border-top-width: 0
    }

    .xl\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px
    }

    .xl\:border-b-0 {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0
    }

    .xl\:p-6 {
      padding: calc(var(--spacing)*6)
    }

    .xl\:p-8 {
      padding: calc(var(--spacing)*8)
    }

    .xl\:px-6 {
      padding-inline: calc(var(--spacing)*6)
    }

    .xl\:px-10 {
      padding-inline: calc(var(--spacing)*10)
    }

    .xl\:py-6 {
      padding-block: calc(var(--spacing)*6)
    }

    .xl\:py-12 {
      padding-block: calc(var(--spacing)*12)
    }

    .xl\:py-\[27px\] {
      padding-block: 27px
    }

    .xl\:pt-0 {
      padding-top: calc(var(--spacing)*0)
    }

    .xl\:pr-5 {
      padding-right: calc(var(--spacing)*5)
    }

    .xl\:pb-0 {
      padding-bottom: calc(var(--spacing)*0)
    }

    .xl\:text-left {
      text-align: left
    }

    .xl\:text-title-2xl {
      font-size: var(--text-title-2xl);
      line-height: var(--tw-leading, var(--text-title-2xl--line-height))
    }

    .xl\:text-title-lg {
      font-size: var(--text-title-lg);
      line-height: var(--tw-leading, var(--text-title-lg--line-height))
    }

    .xl\:text-title-xl {
      font-size: var(--text-title-xl);
      line-height: var(--tw-leading, var(--text-title-xl--line-height))
    }
  }

  @media (width>=1536px) {
    .\32xl\:col-span-4 {
      grid-column: span 4/span 4
    }

    .\32xl\:col-span-8 {
      grid-column: span 8/span 8
    }

    .\32xl\:max-h-\[630px\] {
      max-height: 630px
    }

    .\32xl\:max-h-\[670px\] {
      max-height: 670px
    }

    .\32xl\:max-h-\[780px\] {
      max-height: 780px
    }

    .\32xl\:w-\[554px\] {
      width: 554px
    }

    .\32xl\:min-w-\[808px\] {
      min-width: 808px
    }

    .\32xl\:min-w-full {
      min-width: 100%
    }

    .\32xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr))
    }

    .\32xl\:gap-x-32 {
      column-gap: calc(var(--spacing)*32)
    }
  }

  .dark\:block:is(.dark *) {
    display: block
  }

  .dark\:hidden:is(.dark *) {
    display: none
  }

  :where(.dark\:divide-gray-800:is(.dark *)>:not(:last-child)) {
    border-color: var(--color-gray-800)
  }

  :where(.dark\:divide-white\/\[0\.05\]:is(.dark *)>:not(:last-child)) {
    border-color: color-mix(in oklab, var(--color-white)5%, transparent)
  }

  .dark\:border-blue-light-500\/30:is(.dark *) {
    border-color: color-mix(in oklab, var(--color-blue-light-500)30%, transparent)
  }

  .dark\:border-brand-200:is(.dark *) {
    border-color: var(--color-brand-200)
  }

  .dark\:border-brand-400:is(.dark *) {
    border-color: var(--color-brand-400)
  }

  .dark\:border-brand-500:is(.dark *) {
    border-color: var(--color-brand-500)
  }

  .dark\:border-error-500:is(.dark *) {
    border-color: var(--color-error-500)
  }

  .dark\:border-error-500\/30:is(.dark *) {
    border-color: color-mix(in oklab, var(--color-error-500)30%, transparent)
  }

  .dark\:border-gray-700:is(.dark *) {
    border-color: var(--color-gray-700)
  }

  .dark\:border-gray-800:is(.dark *) {
    border-color: var(--color-gray-800)
  }

  .dark\:border-gray-900:is(.dark *) {
    border-color: var(--color-gray-900)
  }

  .dark\:border-success-500:is(.dark *) {
    border-color: var(--color-success-500)
  }

  .dark\:border-success-500\/30:is(.dark *) {
    border-color: color-mix(in oklab, var(--color-success-500)30%, transparent)
  }

  .dark\:border-warning-500\/30:is(.dark *) {
    border-color: color-mix(in oklab, var(--color-warning-500)30%, transparent)
  }

  .dark\:border-white\/10:is(.dark *) {
    border-color: color-mix(in oklab, var(--color-white)10%, transparent)
  }

  .dark\:border-white\/\[0\.03\]:is(.dark *) {
    border-color: color-mix(in oklab, var(--color-white)3%, transparent)
  }

  .dark\:border-white\/\[0\.05\]:is(.dark *) {
    border-color: color-mix(in oklab, var(--color-white)5%, transparent)
  }

  .dark\:bg-\[\#1E2634\]:is(.dark *) {
    background-color: #1e2634
  }

  .dark\:bg-\[\#1E2635\]:is(.dark *) {
    background-color: #1e2635
  }

  .dark\:bg-\[\#1e2636\]:is(.dark *) {
    background-color: #1e2636
  }

  .dark\:bg-\[\#171f2f\]:is(.dark *) {
    background-color: #171f2f
  }

  .dark\:bg-\[\#252D3A\]:is(.dark *) {
    background-color: #252d3a
  }

  .dark\:bg-\[\#353C49\]:is(.dark *) {
    background-color: #353c49
  }

  .dark\:bg-blue-light-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-blue-light-500)15%, transparent)
  }

  .dark\:bg-brand-100:is(.dark *) {
    background-color: var(--color-brand-100)
  }

  .dark\:bg-brand-400\/20:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-400)20%, transparent)
  }

  .dark\:bg-brand-500:is(.dark *) {
    background-color: var(--color-brand-500)
  }

  .dark\:bg-brand-500\/10:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-500)10%, transparent)
  }

  .dark\:bg-brand-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-500)15%, transparent)
  }

  .dark\:bg-brand-500\/\[0\.12\]:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-brand-500)12%, transparent)
  }

  .dark\:bg-error-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-error-500)15%, transparent)
  }

  .dark\:bg-gray-400:is(.dark *) {
    background-color: var(--color-gray-400)
  }

  .dark\:bg-gray-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-gray-500)15%, transparent)
  }

  .dark\:bg-gray-700:is(.dark *) {
    background-color: var(--color-gray-700)
  }

  .dark\:bg-gray-800:is(.dark *) {
    background-color: var(--color-gray-800)
  }

  .dark\:bg-gray-800\/\[0\.03\]:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-gray-800)3%, transparent)
  }

  .dark\:bg-gray-900:is(.dark *) {
    background-color: var(--color-gray-900)
  }

  .dark\:bg-gray-dark:is(.dark *) {
    background-color: var(--color-gray-dark)
  }

  .dark\:bg-orange-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-orange-500)15%, transparent)
  }

  .dark\:bg-purple-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-purple-500)15%, transparent)
  }

  .dark\:bg-success-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-success-500)15%, transparent)
  }

  .dark\:bg-transparent:is(.dark *) {
    background-color: #0000
  }

  .dark\:bg-warning-500\/15:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-warning-500)15%, transparent)
  }

  .dark\:bg-white\/5:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-white)5%, transparent)
  }

  .dark\:bg-white\/10:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-white)10%, transparent)
  }

  .dark\:bg-white\/\[0\.03\]:is(.dark *) {
    background-color: color-mix(in oklab, var(--color-white)3%, transparent)
  }

  .dark\:fill-blue-light-500:is(.dark *) {
    fill: var(--color-blue-light-500)
  }

  .dark\:fill-blue-light-500\/15:is(.dark *) {
    fill: color-mix(in oklab, var(--color-blue-light-500)15%, transparent)
  }

  .dark\:fill-error-500:is(.dark *) {
    fill: var(--color-error-500)
  }

  .dark\:fill-error-500\/15:is(.dark *) {
    fill: color-mix(in oklab, var(--color-error-500)15%, transparent)
  }

  .dark\:fill-gray-200:is(.dark *) {
    fill: var(--color-gray-200)
  }

  .dark\:fill-gray-400:is(.dark *) {
    fill: var(--color-gray-400)
  }

  .dark\:fill-gray-800:is(.dark *) {
    fill: var(--color-gray-800)
  }

  .dark\:fill-orange-400:is(.dark *) {
    fill: var(--color-orange-400)
  }

  .dark\:fill-success-500:is(.dark *) {
    fill: var(--color-success-500)
  }

  .dark\:fill-success-500\/15:is(.dark *) {
    fill: color-mix(in oklab, var(--color-success-500)15%, transparent)
  }

  .dark\:fill-warning-500\/15:is(.dark *) {
    fill: color-mix(in oklab, var(--color-warning-500)15%, transparent)
  }

  .dark\:stroke-gray-400:is(.dark *) {
    stroke: var(--color-gray-400)
  }

  .dark\:text-blue-light-500:is(.dark *) {
    color: var(--color-blue-light-500)
  }

  .dark\:text-brand-400:is(.dark *) {
    color: var(--color-brand-400)
  }

  .dark\:text-brand-500:is(.dark *) {
    color: var(--color-brand-500)
  }

  .dark\:text-error-400:is(.dark *) {
    color: var(--color-error-400)
  }

  .dark\:text-error-500:is(.dark *) {
    color: var(--color-error-500)
  }

  .dark\:text-gray-200:is(.dark *) {
    color: var(--color-gray-200)
  }

  .dark\:text-gray-300:is(.dark *) {
    color: var(--color-gray-300)
  }

  .dark\:text-gray-400:is(.dark *) {
    color: var(--color-gray-400)
  }

  .dark\:text-gray-400\/10:is(.dark *) {
    color: color-mix(in oklab, var(--color-gray-400)10%, transparent)
  }

  .dark\:text-gray-400\/25:is(.dark *) {
    color: color-mix(in oklab, var(--color-gray-400)25%, transparent)
  }

  .dark\:text-gray-400\/50:is(.dark *) {
    color: color-mix(in oklab, var(--color-gray-400)50%, transparent)
  }

  .dark\:text-gray-400\/75:is(.dark *) {
    color: color-mix(in oklab, var(--color-gray-400)75%, transparent)
  }

  .dark\:text-gray-400\/90:is(.dark *) {
    color: color-mix(in oklab, var(--color-gray-400)90%, transparent)
  }

  .dark\:text-gray-500:is(.dark *) {
    color: var(--color-gray-500)
  }

  .dark\:text-gray-600:is(.dark *) {
    color: var(--color-gray-600)
  }

  .dark\:text-gray-700:is(.dark *) {
    color: var(--color-gray-700)
  }

  .dark\:text-gray-800:is(.dark *) {
    color: var(--color-gray-800)
  }

  .dark\:text-orange-400:is(.dark *) {
    color: var(--color-orange-400)
  }

  .dark\:text-purple-400:is(.dark *) {
    color: var(--color-purple-400)
  }

  .dark\:text-success-400:is(.dark *) {
    color: var(--color-success-400)
  }

  .dark\:text-success-500:is(.dark *) {
    color: var(--color-success-500)
  }

  .dark\:text-warning-500:is(.dark *) {
    color: var(--color-warning-500)
  }

  .dark\:text-white:is(.dark *) {
    color: var(--color-white)
  }

  .dark\:text-white\/60:is(.dark *) {
    color: color-mix(in oklab, var(--color-white)60%, transparent)
  }

  .dark\:text-white\/70:is(.dark *) {
    color: color-mix(in oklab, var(--color-white)70%, transparent)
  }

  .dark\:text-white\/80:is(.dark *) {
    color: color-mix(in oklab, var(--color-white)80%, transparent)
  }

  .dark\:text-white\/90:is(.dark *),
  .dark\:text-white\/\[0\.90\]:is(.dark *) {
    color: color-mix(in oklab, var(--color-white)90%, transparent)
  }

  .dark\:ring-gray-700:is(.dark *) {
    --tw-ring-color: var(--color-gray-700)
  }

  .dark\:ring-gray-800:is(.dark *) {
    --tw-ring-color: var(--color-gray-800)
  }

  .dark\:ring-gray-800\/90:is(.dark *) {
    --tw-ring-color: color-mix(in oklab, var(--color-gray-800)90%, transparent)
  }

  @media (hover:hover) {
    .dark\:group-hover\:bg-brand-500\/15:is(.dark *):is(:where(.group):hover *) {
      background-color: color-mix(in oklab, var(--color-brand-500)15%, transparent)
    }

    .dark\:group-hover\:fill-gray-200:is(.dark *):is(:where(.group):hover *) {
      fill: var(--color-gray-200)
    }

    .dark\:group-hover\:fill-gray-300:is(.dark *):is(:where(.group):hover *) {
      fill: var(--color-gray-300)
    }

    .dark\:group-hover\:text-brand-400:is(.dark *):is(:where(.group):hover *) {
      color: var(--color-brand-400)
    }
  }

  .dark\:file\:border-gray-800:is(.dark *)::file-selector-button {
    border-color: var(--color-gray-800)
  }

  .dark\:file\:bg-white\/\[0\.03\]:is(.dark *)::file-selector-button {
    background-color: color-mix(in oklab, var(--color-white)3%, transparent)
  }

  .dark\:file\:text-gray-400:is(.dark *)::file-selector-button {
    color: var(--color-gray-400)
  }

  .dark\:placeholder\:text-gray-400:is(.dark *)::placeholder {
    color: var(--color-gray-400)
  }

  .dark\:placeholder\:text-white\/30:is(.dark *)::placeholder {
    color: color-mix(in oklab, var(--color-white)30%, transparent)
  }

  .dark\:placeholder\:text-white\/90:is(.dark *)::placeholder {
    color: color-mix(in oklab, var(--color-white)90%, transparent)
  }

  @media (hover:hover) {
    .dark\:hover\:border-brand-500:is(.dark *):hover {
      border-color: var(--color-brand-500)
    }

    .dark\:hover\:border-gray-800:is(.dark *):hover {
      border-color: var(--color-gray-800)
    }

    .dark\:hover\:border-white\/\[0\.05\]:is(.dark *):hover {
      border-color: color-mix(in oklab, var(--color-white)5%, transparent)
    }

    .dark\:hover\:bg-brand-500:is(.dark *):hover {
      background-color: var(--color-brand-500)
    }

    .dark\:hover\:bg-brand-500\/\[0\.12\]:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-brand-500)12%, transparent)
    }

    .dark\:hover\:bg-brand-600:is(.dark *):hover {
      background-color: var(--color-brand-600)
    }

    .dark\:hover\:bg-gray-700:is(.dark *):hover {
      background-color: var(--color-gray-700)
    }

    .dark\:hover\:bg-gray-800:is(.dark *):hover {
      background-color: var(--color-gray-800)
    }

    .dark\:hover\:bg-white\/5:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-white)5%, transparent)
    }

    .dark\:hover\:bg-white\/10:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-white)10%, transparent)
    }

    .dark\:hover\:bg-white\/\[0\.03\]:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-white)3%, transparent)
    }

    .dark\:hover\:bg-white\/\[0\.05\]:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-white)5%, transparent)
    }

    .dark\:hover\:bg-white\/\[0\.07\]:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--color-white)7%, transparent)
    }

    .dark\:hover\:text-brand-400:is(.dark *):hover {
      color: var(--color-brand-400)
    }

    .dark\:hover\:text-brand-500:is(.dark *):hover {
      color: var(--color-brand-500)
    }

    .dark\:hover\:text-error-500:is(.dark *):hover {
      color: var(--color-error-500)
    }

    .dark\:hover\:text-gray-200:is(.dark *):hover {
      color: var(--color-gray-200)
    }

    .dark\:hover\:text-gray-300:is(.dark *):hover {
      color: var(--color-gray-300)
    }

    .dark\:hover\:text-gray-400\/10:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-gray-400)10%, transparent)
    }

    .dark\:hover\:text-gray-400\/25:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-gray-400)25%, transparent)
    }

    .dark\:hover\:text-gray-400\/50:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-gray-400)50%, transparent)
    }

    .dark\:hover\:text-gray-400\/75:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-gray-400)75%, transparent)
    }

    .dark\:hover\:text-gray-400\/100:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-gray-400)100%, transparent)
    }

    .dark\:hover\:text-white:is(.dark *):hover {
      color: var(--color-white)
    }

    .dark\:hover\:text-white\/70:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-white)70%, transparent)
    }

    .dark\:hover\:text-white\/80:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-white)80%, transparent)
    }

    .dark\:hover\:text-white\/90:is(.dark *):hover {
      color: color-mix(in oklab, var(--color-white)90%, transparent)
    }
  }

  .dark\:focus\:border-brand-300:is(.dark *):focus {
    border-color: var(--color-brand-300)
  }

  .dark\:focus\:border-brand-800:is(.dark *):focus {
    border-color: var(--color-brand-800)
  }

  .dark\:focus\:border-error-800:is(.dark *):focus {
    border-color: var(--color-error-800)
  }

  .dark\:focus\:border-success-800:is(.dark *):focus {
    border-color: var(--color-success-800)
  }

  .\[\&\:\:-webkit-scrollbar\]\:h-1\.5::-webkit-scrollbar {
    height: calc(var(--spacing)*1.5)
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-full::-webkit-scrollbar-thumb {
    border-radius: 3.40282e38px
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:bg-gray-100::-webkit-scrollbar-thumb {
    background-color: var(--color-gray-100)
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:bg-gray-200::-webkit-scrollbar-thumb {
    background-color: var(--color-gray-200)
  }

  .dark\:\[\&\:\:-webkit-scrollbar-thumb\]\:bg-gray-600:is(.dark *)::-webkit-scrollbar-thumb {
    background-color: var(--color-gray-600)
  }

  .\[\&\:\:-webkit-scrollbar-track\]\:bg-white::-webkit-scrollbar-track {
    background-color: var(--color-white)
  }

  .dark\:\[\&\:\:-webkit-scrollbar-track\]\:bg-transparent:is(.dark *)::-webkit-scrollbar-track {
    background-color: #0000
  }

  input[type=date]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none
  }

  input[type=time]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none
  }

  input[type=date]::-webkit-calendar-picker-indicator {
    -webkit-appearance: none;
    display: none
  }

  input[type=time]::-webkit-calendar-picker-indicator {
    -webkit-appearance: none;
    display: none
  }
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #344054
}

.tableCheckbox:checked~span span {
  opacity: 1
}

.tableCheckbox:checked~span {
  border-color: var(--color-brand-500);
  background-color: var(--color-brand-500)
}

.apexcharts-legend-text {
  color: var(--color-gray-700) !important
}

.apexcharts-text {
  fill: var(--color-gray-700) !important
}

.apexcharts-text:is(.dark *) {
  fill: var(--color-gray-400) !important
}

.apexcharts-tooltip.apexcharts-theme-light {
  gap: calc(var(--spacing)*1);
  padding: calc(var(--spacing)*3);
  border-radius: var(--radius-lg) !important;
  border-color: var(--color-gray-200) !important;
  --tw-shadow: 0px 1px 3px 0px var(--tw-shadow-color, #1018281a), 0px 1px 2px 0px var(--tw-shadow-color, #1018280f) !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important
}

.apexcharts-tooltip.apexcharts-theme-light:is(.dark *) {
  border-color: var(--color-gray-800) !important;
  background-color: var(--color-gray-900) !important
}

.apexcharts-tooltip-marker {
  width: 6px;
  height: 6px;
  margin-right: 6px
}

.apexcharts-legend-text {
  padding-left: calc(var(--spacing)*5) !important;
  color: var(--color-gray-700) !important
}

.apexcharts-legend-text:is(.dark *) {
  color: var(--color-gray-400) !important
}

.apexcharts-tooltip-series-group,
.apexcharts-tooltip-y-group {
  padding: calc(var(--spacing)*0) !important
}

.apexcharts-tooltip-title {
  margin-bottom: calc(var(--spacing)*0) !important;
  border-bottom-style: var(--tw-border-style) !important;
  padding: calc(var(--spacing)*0) !important;
  --tw-leading: calc(var(--spacing)*4) !important;
  font-size: 10px !important;
  line-height: calc(var(--spacing)*4) !important;
  color: var(--color-gray-800) !important;
  background-color: #0000 !important;
  border-bottom-width: 0 !important
}

.apexcharts-tooltip-title:is(.dark *) {
  color: color-mix(in oklab, var(--color-white)90%, transparent) !important
}

.apexcharts-tooltip-text {
  font-size: var(--text-theme-xs) !important;
  line-height: var(--tw-leading, var(--text-theme-xs--line-height)) !important;
  color: var(--color-gray-700) !important
}

.apexcharts-tooltip-text:is(.dark *) {
  color: color-mix(in oklab, var(--color-white)90%, transparent) !important
}

.apexcharts-tooltip-text-y-value {
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important
}

.apexcharts-gridline {
  stroke: var(--color-gray-100) !important
}

.apexcharts-gridline:is(.dark *) {
  stroke: var(--color-gray-800) !important
}

#chartTwo .apexcharts-datalabels-group {
  --tw-translate-y: calc(var(--spacing)*-24) !important;
  translate: var(--tw-translate-x)var(--tw-translate-y) !important
}

#chartTwo .apexcharts-datalabels-group .apexcharts-text {
  fill: var(--color-gray-800) !important;
  --tw-font-weight: var(--font-weight-semibold) !important;
  font-weight: var(--font-weight-semibold) !important
}

#chartTwo .apexcharts-datalabels-group .apexcharts-text:is(.dark *) {
  fill: color-mix(in oklab, var(--color-white)90%, transparent) !important
}

#chartDarkStyle .apexcharts-datalabels-group .apexcharts-text {
  fill: var(--color-gray-800) !important;
  --tw-font-weight: var(--font-weight-semibold) !important;
  font-weight: var(--font-weight-semibold) !important
}

#chartDarkStyle .apexcharts-datalabels-group .apexcharts-text:is(.dark *) {
  fill: color-mix(in oklab, var(--color-white)90%, transparent) !important
}

#chartSixteen .apexcharts-legend {
  padding: calc(var(--spacing)*0) !important;
  padding-left: calc(var(--spacing)*6) !important
}

.jvectormap-container {
  background-color: var(--color-gray-50) !important
}

.jvectormap-container:is(.dark *) {
  background-color: var(--color-gray-900) !important
}

.jvectormap-region.jvectormap-element {
  fill: var(--color-gray-300) !important
}

@media (hover:hover) {
  .jvectormap-region.jvectormap-element:hover {
    fill: var(--color-brand-500) !important
  }
}

.jvectormap-region.jvectormap-element:is(.dark *) {
  fill: var(--color-gray-700) !important
}

@media (hover:hover) {
  .jvectormap-region.jvectormap-element:is(.dark *):hover {
    fill: var(--color-brand-500) !important
  }
}

.jvectormap-marker.jvectormap-element {
  stroke: var(--color-gray-200) !important
}

.jvectormap-marker.jvectormap-element:is(.dark *) {
  stroke: var(--color-gray-800) !important
}

.jvectormap-tip {
  --tw-border-style: none !important;
  background-color: var(--color-brand-500) !important;
  padding-inline: calc(var(--spacing)*2) !important;
  padding-block: calc(var(--spacing)*1) !important;
  border-style: none !important
}

.jvectormap-zoomin,
.jvectormap-zoomout {
  display: none !important
}

.stocks-slider-outer .swiper-button-next:after,
.stocks-slider-outer .swiper-button-prev:after {
  display: none
}

.stocks-slider-outer .swiper-button-next,
.stocks-slider-outer .swiper-button-prev {
  margin-top: calc(var(--spacing)*0);
  height: calc(var(--spacing)*8);
  width: calc(var(--spacing)*9);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  border-radius: 3.40282e38px;
  color: var(--color-gray-700) !important;
  position: static !important
}

@media (hover:hover) {
  :is(.stocks-slider-outer .swiper-button-next, .stocks-slider-outer .swiper-button-prev):hover {
    background-color: var(--color-gray-100)
  }
}

:is(.stocks-slider-outer .swiper-button-next, .stocks-slider-outer .swiper-button-prev):is(.dark *) {
  border-color: color-mix(in oklab, var(--color-white)3%, transparent);
  background-color: var(--color-gray-800);
  color: var(--color-gray-400) !important
}

@media (hover:hover) {
  :is(.stocks-slider-outer .swiper-button-next, .stocks-slider-outer .swiper-button-prev):is(.dark *):hover {
    background-color: color-mix(in oklab, var(--color-white)5%, transparent);
    color: color-mix(in oklab, var(--color-white)90%, transparent) !important
  }
}

.stocks-slider-outer .swiper-button-next.swiper-button-disabled,
.stocks-slider-outer .swiper-button-prev.swiper-button-disabled {
  background-color: var(--color-white);
  opacity: .5
}

:is(.stocks-slider-outer .swiper-button-next.swiper-button-disabled, .stocks-slider-outer .swiper-button-prev.swiper-button-disabled):is(.dark *) {
  background-color: var(--color-gray-900)
}

.stocks-slider-outer .swiper-button-next svg,
.stocks-slider-outer .swiper-button-prev svg {
  width: auto !important;
  height: auto !important
}

.flatpickr-wrapper {
  width: 100%
}

.flatpickr-calendar {
  margin-top: calc(var(--spacing)*2);
  border-radius: var(--radius-xl) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 1px !important;
  border-color: var(--color-gray-200) !important;
  background-color: var(--color-white) !important;
  padding: calc(var(--spacing)*5) !important;
  color: var(--color-gray-500) !important
}

@media (width>=375px) {
  .flatpickr-calendar {
    width: auto !important
  }
}

.flatpickr-calendar:is(.dark *) {
  border-color: color-mix(in oklab, var(--color-white)5%, transparent) !important;
  background-color: var(--color-gray-dark) !important;
  color: var(--color-gray-400) !important;
  --tw-shadow: 0px 20px 24px -4px var(--tw-shadow-color, #10182814), 0px 8px 8px -4px var(--tw-shadow-color, #10182808) !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  stroke: var(--color-brand-500)
}

.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  display: none
}

.flatpickr-current-month {
  padding: calc(var(--spacing)*0) !important
}

.flatpickr-current-month .cur-month,
.flatpickr-current-month input.cur-year {
  height: auto !important;
  padding-top: calc(var(--spacing)*0) !important;
  font-size: var(--text-lg) !important;
  line-height: var(--tw-leading, var(--text-lg--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-gray-800) !important
}

:is(.flatpickr-current-month .cur-month, .flatpickr-current-month input.cur-year):is(.dark *) {
  color: color-mix(in oklab, var(--color-white)90%, transparent) !important
}

.flatpickr-prev-month,
.flatpickr-next-month {
  padding: calc(var(--spacing)*0) !important
}

.flatpickr-weekdays {
  margin-top: calc(var(--spacing)*6);
  margin-bottom: calc(var(--spacing)*4);
  height: auto;
  background-color: #0000 !important
}

.flatpickr-weekday {
  font-size: var(--text-theme-sm) !important;
  line-height: var(--tw-leading, var(--text-theme-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-gray-500) !important;
  background-color: #0000 !important
}

.flatpickr-weekday:is(.dark *) {
  color: var(--color-gray-400) !important
}

.flatpickr-day {
  font-size: var(--text-theme-sm) !important;
  line-height: var(--tw-leading, var(--text-theme-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-gray-800) !important;
  align-items: center !important;
  display: flex !important
}

.flatpickr-day:is(.dark *) {
  color: color-mix(in oklab, var(--color-white)90%, transparent) !important
}

@media (hover:hover) {
  .flatpickr-day:is(.dark *):hover {
    border-color: var(--color-gray-300) !important;
    background-color: var(--color-gray-900) !important
  }
}

.flatpickr-day.nextMonthDay,
.flatpickr-day.prevMonthDay {
  color: var(--color-gray-400) !important
}

.flatpickr-months>.flatpickr-month {
  background: 0 0 !important
}

.flatpickr-month .flatpickr-current-month .flatpickr-monthDropdown-months {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-weight: 500;
  background: 0 0 !important
}

.flatpickr-month .flatpickr-current-month .flatpickr-monthDropdown-months:focus {
  border: 0 !important;
  outline: none !important
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  top: calc(var(--spacing)*7) !important;
  background-color: #0000 !important
}

:is(.flatpickr-months .flatpickr-prev-month, .flatpickr-months .flatpickr-next-month):is(.dark *) {
  fill: var(--color-white) !important;
  color: var(--color-white) !important
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  left: calc(var(--spacing)*7) !important
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  right: calc(var(--spacing)*7) !important
}

.flatpickr-days {
  border-style: var(--tw-border-style) !important;
  border-width: 0 !important
}

span.flatpickr-weekday,
.flatpickr-months .flatpickr-month {
  background-image: none !important
}

:is(span.flatpickr-weekday, .flatpickr-months .flatpickr-month):is(.dark *) {
  fill: var(--color-white) !important;
  color: var(--color-white) !important
}

.flatpickr-rContainer:is(.dark *) {
  background-color: var(--color-gray-dark)
}

.flatpickr-innerContainer {
  border-bottom-style: var(--tw-border-style) !important;
  border-bottom-width: 0 !important
}

.flatpickr-months .flatpickr-month {
  background-image: none !important
}

.flatpickr-day.inRange {
  box-shadow: -5px 0 #f9fafb, 5px 0 #f9fafb !important
}

.flatpickr-day.inRange:is(.dark *) {
  --tw-shadow: -5px 0 0 var(--tw-shadow-color, #262d3c), 5px 0 0 var(--tw-shadow-color, #262d3c) !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  border-color: var(--color-gray-50) !important;
  background-color: var(--color-gray-50) !important
}

:is(.flatpickr-day.inRange, .flatpickr-day.prevMonthDay.inRange, .flatpickr-day.nextMonthDay.inRange, .flatpickr-day.today.inRange, .flatpickr-day.prevMonthDay.today.inRange, .flatpickr-day.nextMonthDay.today.inRange, .flatpickr-day:hover, .flatpickr-day.prevMonthDay:hover, .flatpickr-day.nextMonthDay:hover, .flatpickr-day:focus, .flatpickr-day.prevMonthDay:focus, .flatpickr-day.nextMonthDay:focus):is(.dark *) {
  border-style: var(--tw-border-style) !important;
  border-width: 0 !important;
  border-color: color-mix(in oklab, var(--color-white)5%, transparent) !important;
  background-color: color-mix(in oklab, var(--color-white)5%, transparent) !important
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
:is(.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange):is(.dark *) {
  color: var(--color-white) !important
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #465fff;
  border-color: var(--color-brand-500) !important;
  background-color: var(--color-brand-500) !important
}

@media (hover:hover) {
  :is(.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange, .flatpickr-day.selected.inRange, .flatpickr-day.startRange.inRange, .flatpickr-day.endRange.inRange, .flatpickr-day.selected:focus, .flatpickr-day.startRange:focus, .flatpickr-day.endRange:focus, .flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover, .flatpickr-day.selected.prevMonthDay, .flatpickr-day.startRange.prevMonthDay, .flatpickr-day.endRange.prevMonthDay, .flatpickr-day.selected.nextMonthDay, .flatpickr-day.startRange.nextMonthDay, .flatpickr-day.endRange.nextMonthDay):hover {
    border-color: var(--color-brand-500) !important;
    background-color: var(--color-brand-500) !important
  }
}

.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)) {
  box-shadow: -10px 0 #465fff
}

@media (hover:hover) {
  :is(.flatpickr-months .flatpickr-prev-month svg, .flatpickr-months .flatpickr-next-month svg, .flatpickr-months .flatpickr-prev-month, .flatpickr-months .flatpickr-next-month):hover {
    fill: none !important
  }
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: none !important
}

.flatpickr-calendar.static {
  right: calc(var(--spacing)*0)
}

.fc .fc-view-harness {
  max-width: 100%;
  overflow-x: auto
}

.fc .fc-view-harness::-webkit-scrollbar {
  width: calc(var(--spacing)*1.5);
  height: calc(var(--spacing)*1.5)
}

.fc .fc-view-harness::-webkit-scrollbar-track {
  border-radius: 3.40282e38px
}

.fc .fc-view-harness::-webkit-scrollbar-thumb {
  background-color: var(--color-gray-200);
  border-radius: 3.40282e38px
}

.fc .fc-view-harness::-webkit-scrollbar-thumb:is() {
  background-color: var(--color-gray-700)
}

.fc-dayGridMonth-view.fc-view.fc-daygrid {
  min-width: 718px
}

.fc .fc-scrollgrid-section>* {
  border-bottom-width: 0;
  border-right-width: 0
}

.fc .fc-scrollgrid {
  border-left-width: 0
}

.fc .fc-toolbar.fc-header-toolbar {
  gap: calc(var(--spacing)*4);
  padding-inline: calc(var(--spacing)*6);
  padding-top: calc(var(--spacing)*6);
  flex-direction: column
}

@media (width>=640px) {
  .fc .fc-toolbar.fc-header-toolbar {
    flex-direction: row
  }
}

.fc-button-group {
  gap: calc(var(--spacing)*2)
}

.fc-button-group .fc-button {
  height: calc(var(--spacing)*10);
  width: calc(var(--spacing)*10);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  background-color: #0000;
  justify-content: center;
  align-items: center;
  display: flex;
  border-radius: var(--radius-lg) !important
}

@media (hover:hover) {
  .fc-button-group .fc-button:hover {
    border-color: var(--color-gray-200);
    background-color: var(--color-gray-50)
  }
}

.fc-button-group .fc-button:focus {
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
}

.fc-button-group .fc-button:active {
  border-color: var(--color-gray-200) !important;
  --tw-shadow: 0 0 #0000 !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  background-color: #0000 !important
}

.fc-button-group .fc-button:is(.dark *) {
  border-color: var(--color-gray-800)
}

@media (hover:hover) {
  .fc-button-group .fc-button:is(.dark *):hover {
    border-color: var(--color-gray-800);
    background-color: var(--color-gray-900)
  }
}

.fc-button-group .fc-button:is(.dark *):active {
  border-color: var(--color-gray-800) !important
}

.fc-button-group .fc-button.fc-prev-button:before {
  margin-top: calc(var(--spacing)*1);
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.0068 6L9.75684 12.25L16.0068 18.5' stroke='%23344054' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3C/svg%3E%0A");
  display: inline-block
}

.fc-button-group .fc-button.fc-next-button:before {
  margin-top: calc(var(--spacing)*1);
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.50684 19L15.7568 12.75L9.50684 6.5' stroke='%23344054' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3C/svg%3E%0A");
  display: inline-block
}

.dark .fc-button-group .fc-button.fc-prev-button:before {
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.0068 6L9.75684 12.25L16.0068 18.5' stroke='%2398A2B3' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3C/svg%3E%0A")
}

.dark .fc-button-group .fc-button.fc-next-button:before {
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.50684 19L15.7568 12.75L9.50684 6.5' stroke='%2398A2B3' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3C/svg%3E%0A")
}

.fc-button-group .fc-button .fc-icon {
  display: none
}

.fc-addEventButton-button {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  background-color: var(--color-brand-500) !important;
  padding-inline: calc(var(--spacing)*4) !important;
  padding-block: calc(var(--spacing)*2.5) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  border-width: 0 !important
}

@media (hover:hover) {
  .fc-addEventButton-button:hover {
    background-color: var(--color-brand-600) !important
  }
}

.fc-addEventButton-button:focus {
  --tw-shadow: 0 0 #0000 !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important
}

.fc-toolbar-title {
  color: var(--color-gray-800);
  font-size: var(--text-lg) !important;
  line-height: var(--tw-leading, var(--text-lg--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important
}

.fc-toolbar-title:is(.dark *) {
  color: color-mix(in oklab, var(--color-white)90%, transparent)
}

.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child {
  border-radius: var(--radius-lg);
  background-color: var(--color-gray-100);
  padding: calc(var(--spacing)*.5)
}

.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child:is(.dark *) {
  background-color: var(--color-gray-900)
}

.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button {
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-500);
  background-color: #0000;
  border-style: var(--tw-border-style) !important;
  width: auto !important;
  height: auto !important;
  padding-inline: calc(var(--spacing)*5) !important;
  padding-block: calc(var(--spacing)*2) !important;
  border-width: 0 !important
}

@media (hover:hover) {
  .fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button:hover {
    color: var(--color-gray-700)
  }
}

.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button:focus {
  --tw-shadow: 0 0 #0000 !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important
}

.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button:is(.dark *) {
  color: var(--color-gray-400)
}

.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button.fc-button-active {
  background-color: var(--color-white);
  color: var(--color-gray-900)
}

.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button.fc-button-active:is(.dark *) {
  background-color: var(--color-gray-800);
  color: var(--color-white)
}

.fc-theme-standard th {
  border-top-style: var(--tw-border-style);
  background-color: var(--color-gray-50);
  border-top-width: 1px;
  border-inline-style: var(--tw-border-style) !important;
  border-inline-width: 0 !important;
  border-color: var(--color-gray-200) !important;
  text-align: left !important
}

.fc-theme-standard th:is(.dark *) {
  background-color: var(--color-gray-900);
  border-color: var(--color-gray-800) !important
}

.fc-theme-standard td,
.fc-theme-standard .fc-scrollgrid {
  border-color: var(--color-gray-200) !important
}

:is(.fc-theme-standard td, .fc-theme-standard .fc-scrollgrid):is(.dark *) {
  border-color: var(--color-gray-800) !important
}

.fc .fc-col-header-cell-cushion {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-400);
  text-transform: uppercase;
  padding-inline: calc(var(--spacing)*5) !important;
  padding-block: calc(var(--spacing)*4) !important
}

.fc .fc-daygrid-day.fc-day-today {
  background-color: #0000
}

.fc .fc-daygrid-day {
  padding: calc(var(--spacing)*2)
}

.fc .fc-daygrid-day.fc-day-today .fc-scrollgrid-sync-inner {
  border-radius: var(--radius-sm);
  background-color: var(--color-gray-100)
}

.fc .fc-daygrid-day.fc-day-today .fc-scrollgrid-sync-inner:is(.dark *) {
  background-color: color-mix(in oklab, var(--color-white)3%, transparent)
}

.fc .fc-daygrid-day-number {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  padding: calc(var(--spacing)*3) !important
}

.fc .fc-daygrid-day-number:is(.dark *) {
  color: var(--color-gray-400)
}

.fc .fc-daygrid-day-top {
  flex-direction: row !important
}

.fc .fc-day-other .fc-daygrid-day-top {
  opacity: 1
}

.fc .fc-day-other .fc-daygrid-day-top .fc-daygrid-day-number {
  color: var(--color-gray-400)
}

.fc .fc-day-other .fc-daygrid-day-top .fc-daygrid-day-number:is(.dark *) {
  color: color-mix(in oklab, var(--color-white)30%, transparent)
}

.event-fc-color {
  border-radius: var(--radius-lg);
  padding-block: calc(var(--spacing)*2.5);
  padding-right: calc(var(--spacing)*3);
  padding-left: calc(var(--spacing)*4)
}

.event-fc-color .fc-event-title {
  padding: calc(var(--spacing)*0);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-normal);
  color: var(--color-gray-700)
}

.fc-daygrid-event-dot {
  margin-right: calc(var(--spacing)*3);
  margin-left: calc(var(--spacing)*0);
  height: calc(var(--spacing)*5);
  width: calc(var(--spacing)*1);
  border-radius: var(--radius-sm);
  --tw-border-style: none;
  border-style: none
}

.fc-event:focus {
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)
}

.fc-daygrid-event.fc-event-start {
  margin-left: calc(var(--spacing)*3) !important
}

.event-fc-color.fc-bg-success {
  border-color: var(--color-success-50);
  background-color: var(--color-success-50)
}

.event-fc-color.fc-bg-danger {
  border-color: var(--color-error-50);
  background-color: var(--color-error-50)
}

.event-fc-color.fc-bg-primary {
  border-color: var(--color-brand-50);
  background-color: var(--color-brand-50)
}

.event-fc-color.fc-bg-warning {
  border-color: var(--color-orange-50);
  background-color: var(--color-orange-50)
}

.event-fc-color.fc-bg-success .fc-daygrid-event-dot {
  background-color: var(--color-success-500)
}

.event-fc-color.fc-bg-danger .fc-daygrid-event-dot {
  background-color: var(--color-error-500)
}

.event-fc-color.fc-bg-primary .fc-daygrid-event-dot {
  background-color: var(--color-brand-500)
}

.event-fc-color.fc-bg-warning .fc-daygrid-event-dot {
  background-color: var(--color-orange-500)
}

.fc-direction-ltr .fc-timegrid-slot-label-frame {
  padding-inline: calc(var(--spacing)*3);
  padding-block: calc(var(--spacing)*1.5);
  text-align: left;
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-500)
}

.fc-direction-ltr .fc-timegrid-slot-label-frame:is(.dark *) {
  color: var(--color-gray-400)
}

.fc .fc-timegrid-axis-cushion {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-500)
}

.fc .fc-timegrid-axis-cushion:is(.dark *) {
  color: var(--color-gray-400)
}

.input-date-icon::-webkit-inner-spin-button {
  opacity: 0;
  -webkit-appearance: none
}

.input-date-icon::-webkit-calendar-picker-indicator {
  opacity: 0;
  -webkit-appearance: none
}

.swiper-button-prev svg,
.swiper-button-next svg {
  width: auto !important;
  height: auto !important
}

.carouselTwo .swiper-button-next:after,
.carouselTwo .swiper-button-prev:after,
.carouselFour .swiper-button-next:after,
.carouselFour .swiper-button-prev:after {
  display: none
}

.carouselTwo .swiper-button-next.swiper-button-disabled,
.carouselTwo .swiper-button-prev.swiper-button-disabled,
.carouselFour .swiper-button-next.swiper-button-disabled,
.carouselFour .swiper-button-prev.swiper-button-disabled {
  background-color: color-mix(in oklab, var(--color-white)60%, transparent);
  opacity: 1 !important
}

.carouselTwo .swiper-button-next,
.carouselTwo .swiper-button-prev,
.carouselFour .swiper-button-next,
.carouselFour .swiper-button-prev {
  height: calc(var(--spacing)*10);
  width: calc(var(--spacing)*10);
  border-style: var(--tw-border-style);
  border-width: .5px;
  border-color: color-mix(in oklab, var(--color-white)10%, transparent);
  background-color: color-mix(in oklab, var(--color-white)90%, transparent);
  --tw-shadow: 0px 1px 2px 0px var(--tw-shadow-color, #1018281a), 0px 1px 3px 0px var(--tw-shadow-color, #1018281a);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  --tw-backdrop-blur: blur(10px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur, )var(--tw-backdrop-brightness, )var(--tw-backdrop-contrast, )var(--tw-backdrop-grayscale, )var(--tw-backdrop-hue-rotate, )var(--tw-backdrop-invert, )var(--tw-backdrop-opacity, )var(--tw-backdrop-saturate, )var(--tw-backdrop-sepia, );
  backdrop-filter: var(--tw-backdrop-blur, )var(--tw-backdrop-brightness, )var(--tw-backdrop-contrast, )var(--tw-backdrop-grayscale, )var(--tw-backdrop-hue-rotate, )var(--tw-backdrop-invert, )var(--tw-backdrop-opacity, )var(--tw-backdrop-saturate, )var(--tw-backdrop-sepia, );
  border-radius: 3.40282e38px;
  color: var(--color-gray-700) !important
}

.carouselTwo .swiper-button-prev,
.carouselFour .swiper-button-prev {
  left: calc(var(--spacing)*3) !important
}

@media (width>=640px) {
  :is(.carouselTwo .swiper-button-prev, .carouselFour .swiper-button-prev) {
    left: calc(var(--spacing)*4) !important
  }
}

.carouselTwo .swiper-button-next,
.carouselFour .swiper-button-next {
  right: calc(var(--spacing)*3) !important
}

@media (width>=640px) {
  :is(.carouselTwo .swiper-button-next, .carouselFour .swiper-button-next) {
    right: calc(var(--spacing)*4) !important
  }
}

.carouselThree .swiper-pagination,
.carouselFour .swiper-pagination {
  --tw-translate-x: -50%;
  translate: var(--tw-translate-x)var(--tw-translate-y);
  align-items: center;
  gap: calc(var(--spacing)*1.5);
  border-style: var(--tw-border-style);
  border-width: .5px;
  border-color: color-mix(in oklab, var(--color-white)10%, transparent);
  background-color: color-mix(in oklab, var(--color-white)60%, transparent);
  padding-inline: calc(var(--spacing)*2);
  padding-block: calc(var(--spacing)*1.5);
  --tw-shadow: 0px 1px 2px 0px var(--tw-shadow-color, #1018281a), 0px 1px 3px 0px var(--tw-shadow-color, #1018281a);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  --tw-backdrop-blur: blur(10px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur, )var(--tw-backdrop-brightness, )var(--tw-backdrop-contrast, )var(--tw-backdrop-grayscale, )var(--tw-backdrop-hue-rotate, )var(--tw-backdrop-invert, )var(--tw-backdrop-opacity, )var(--tw-backdrop-saturate, )var(--tw-backdrop-sepia, );
  backdrop-filter: var(--tw-backdrop-blur, )var(--tw-backdrop-brightness, )var(--tw-backdrop-contrast, )var(--tw-backdrop-grayscale, )var(--tw-backdrop-hue-rotate, )var(--tw-backdrop-invert, )var(--tw-backdrop-opacity, )var(--tw-backdrop-saturate, )var(--tw-backdrop-sepia, );
  border-radius: 40px;
  display: inline-flex;
  bottom: calc(var(--spacing)*3) !important;
  width: auto !important;
  left: 50% !important
}

@media (width>=640px) {
  :is(.carouselThree .swiper-pagination, .carouselFour .swiper-pagination) {
    bottom: calc(var(--spacing)*5) !important
  }
}

.carouselThree .swiper-pagination-bullet,
.carouselFour .swiper-pagination-bullet {
  height: calc(var(--spacing)*2.5);
  width: calc(var(--spacing)*2.5);
  background-color: var(--color-white);
  opacity: 1;
  --tw-shadow: 0px 1px 2px 0px var(--tw-shadow-color, #1018280d);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  --tw-duration: .2s;
  --tw-ease: var(--ease-in-out);
  transition-duration: .2s;
  transition-timing-function: var(--ease-in-out);
  margin: calc(var(--spacing)*0) !important
}

.carouselThree .swiper-pagination-bullet-active,
.carouselFour .swiper-pagination-bullet-active {
  width: calc(var(--spacing)*6.5);
  border-radius: var(--radius-xl)
}

.form-check-input:checked~span {
  border-style: var(--tw-border-style);
  border-width: 6px;
  border-color: var(--color-brand-500);
  background-color: var(--color-brand-500)
}

.form-check-input:checked~span:is(.dark *) {
  border-color: var(--color-brand-500)
}

.taskCheckbox:checked~.box span {
  background-color: var(--color-brand-500);
  opacity: 1
}

.taskCheckbox:checked~p {
  color: var(--color-gray-400);
  text-decoration-line: line-through
}

.taskCheckbox:checked~.box {
  border-color: var(--color-brand-500);
  background-color: var(--color-brand-500)
}

.taskCheckbox:checked~.box:is(.dark *) {
  border-color: var(--color-brand-500)
}

.task {
  opacity: .8;
  cursor: grabbing;
  border-radius: .75rem;
  transition: all .2s;
  box-shadow: 0 1px 3px #1018281a, 0 1px 2px #1018280f
}

.custom-calendar .fc-h-event {
  color: #000;
  background-color: #0000;
  border: none
}

.fc.fc-media-screen {
  min-height: 100vh
}

.simplebar-scrollbar:before {
  background-color: var(--color-gray-200) !important;
  opacity: 1 !important;
  border-radius: 3.40282e38px !important
}

.simplebar-scrollbar:before:is(),
.dark .simplebar-scrollbar:before {
  background-color: var(--color-gray-700) !important
}

.simplebar-scrollbar.simplebar-visible:before {
  opacity: 1
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0)
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0)
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0)
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0)
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0)
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0
}

@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: ""
}

@keyframes spin {
  to {
    transform: rotate(360deg)
  }
}

@keyframes ping {

  75%,
  to {
    opacity: 0;
    transform: scale(2)
  }
}

@font-face {
  font-family: swiper-icons;
  src: url(data:application/font-woff;charset=utf-8;base64,\ 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);
  font-weight: 400;
  font-style: normal
}

:root {
  --swiper-theme-color: #007aff
}

:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1
}

.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  z-index: 1;
  display: block
}

.swiper-vertical>.swiper-wrapper {
  flex-direction: column
}

.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box
}

.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translateZ(0)
}

.swiper-horizontal {
  touch-action: pan-y
}

.swiper-vertical {
  touch-action: pan-x
}

.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block
}

.swiper-slide-invisible-blank {
  visibility: hidden
}

.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto
}

.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height
}

.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden
}

.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px
}

.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d
}

.swiper-3d {
  perspective: 1200px
}

.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d
}

.swiper-css-mode>.swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none
}

.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar {
  display: none
}

.swiper-css-mode>.swiper-wrapper>.swiper-slide {
  scroll-snap-align: start start
}

.swiper-css-mode.swiper-horizontal>.swiper-wrapper {
  scroll-snap-type: x mandatory
}

.swiper-css-mode.swiper-vertical>.swiper-wrapper {
  scroll-snap-type: y mandatory
}

.swiper-css-mode.swiper-free-mode>.swiper-wrapper {
  scroll-snap-type: none
}

.swiper-css-mode.swiper-free-mode>.swiper-wrapper>.swiper-slide {
  scroll-snap-align: none
}

.swiper-css-mode.swiper-centered>.swiper-wrapper:before {
  content: "";
  flex-shrink: 0;
  order: 9999
}

.swiper-css-mode.swiper-centered>.swiper-wrapper>.swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always
}

.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper>.swiper-slide:first-child {
  margin-inline-start: var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper:before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after)
}

.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper>.swiper-slide:first-child {
  margin-block-start: var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper:before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after)
}

.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10
}

.swiper-3d .swiper-slide-shadow {
  background: #00000026
}

.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, #00000080, #0000)
}

.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, #00000080, #0000)
}

.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, #00000080, #0000)
}

.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, #00000080, #0000)
}

.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent
}

.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear
}

.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff
}

.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000
}

@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0)
  }

  to {
    transform: rotate(360deg)
  }
}

.swiper-virtual .swiper-slide {
  -webkit-backface-visibility: hidden;
  transform: translateZ(0)
}

.swiper-virtual.swiper-css-mode .swiper-wrapper:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none
}

.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper:after {
  height: 1px;
  width: var(--swiper-virtual-size)
}

.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper:after {
  width: 1px;
  height: var(--swiper-virtual-size)
}

:root {
  --swiper-navigation-size: 44px
}

.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color))
}

.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: .35;
  cursor: auto;
  pointer-events: none
}

.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none
}

.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important
}

.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform-origin: center
}

.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg)
}

.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto
}

.swiper-button-lock {
  display: none
}

.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1
}

.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: "prev"
}

.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto
}

.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: "next"
}

.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: .3s opacity;
  transform: translateZ(0);
  z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
  opacity: 0
}

.swiper-pagination-disabled>.swiper-pagination,
.swiper-pagination.swiper-pagination-disabled {
  display: none !important
}

.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal>.swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: var(--swiper-pagination-bottom, 8px);
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%
}

.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(.33);
  position: relative
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active,
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(.33)
}

.swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, .2)
}

button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none
}

.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer
}

.swiper-pagination-bullet:only-child {
  display: none !important
}

.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color))
}

.swiper-vertical>.swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: var(--swiper-pagination-right, 8px);
  left: var(--swiper-pagination-left, auto);
  top: 50%;
  transform: translate3d(0, -50%, 0)
}

.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block
}

.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px
}

.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  transition: .2s transform, .2s top
}

.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px)
}

.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translate(-50%);
  white-space: nowrap
}

.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: .2s transform, .2s left
}

.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: .2s transform, .2s right
}

.swiper-pagination-fraction {
  color: var(--swiper-pagination-fraction-color, inherit)
}

.swiper-pagination-progressbar {
  background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, .25));
  position: absolute
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top
}

.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top
}

.swiper-horizontal>.swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: var(--swiper-pagination-progressbar-size, 4px);
  left: 0;
  top: 0
}

.swiper-vertical>.swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: var(--swiper-pagination-progressbar-size, 4px);
  height: 100%;
  left: 0;
  top: 0
}

.swiper-pagination-lock {
  display: none
}

.swiper-scrollbar {
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  position: relative;
  touch-action: none;
  background: var(--swiper-scrollbar-bg-color, rgba(0, 0, 0, .1))
}

.swiper-scrollbar-disabled>.swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-disabled {
  display: none !important
}

.swiper-horizontal>.swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-horizontal {
  position: absolute;
  left: var(--swiper-scrollbar-sides-offset, 1%);
  bottom: var(--swiper-scrollbar-bottom, 4px);
  top: var(--swiper-scrollbar-top, auto);
  z-index: 50;
  height: var(--swiper-scrollbar-size, 4px);
  width: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%))
}

.swiper-vertical>.swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-vertical {
  position: absolute;
  left: var(--swiper-scrollbar-left, auto);
  right: var(--swiper-scrollbar-right, 4px);
  top: var(--swiper-scrollbar-sides-offset, 1%);
  z-index: 50;
  width: var(--swiper-scrollbar-size, 4px);
  height: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%))
}

.swiper-scrollbar-drag {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--swiper-scrollbar-drag-bg-color, rgba(0, 0, 0, .5));
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  left: 0;
  top: 0
}

.swiper-scrollbar-cursor-drag {
  cursor: move
}

.swiper-scrollbar-lock {
  display: none
}

.swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center
}

.swiper-zoom-container>img,
.swiper-zoom-container>svg,
.swiper-zoom-container>canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain
}

.swiper-slide-zoomed {
  cursor: move;
  touch-action: none
}

.swiper .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000
}

.swiper-free-mode>.swiper-wrapper {
  transition-timing-function: ease-out;
  margin: 0 auto
}

.swiper-grid>.swiper-wrapper {
  flex-wrap: wrap
}

.swiper-grid-column>.swiper-wrapper {
  flex-wrap: wrap;
  flex-direction: column
}

.swiper-fade.swiper-free-mode .swiper-slide {
  transition-timing-function: ease-out
}

.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity
}

.swiper-fade .swiper-slide .swiper-slide {
  pointer-events: none
}

.swiper-fade .swiper-slide-active,
.swiper-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto
}

.swiper.swiper-cube {
  overflow: visible
}

.swiper-cube .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  transform-origin: 0 0;
  width: 100%;
  height: 100%
}

.swiper-cube .swiper-slide .swiper-slide {
  pointer-events: none
}

.swiper-cube.swiper-rtl .swiper-slide {
  transform-origin: 100% 0
}

.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-active .swiper-slide-active {
  pointer-events: auto
}

.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-next,
.swiper-cube .swiper-slide-prev {
  pointer-events: auto;
  visibility: visible
}

.swiper-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: .6;
  z-index: 0
}

.swiper-cube .swiper-cube-shadow:before {
  content: "";
  background: #000;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  filter: blur(50px)
}

.swiper-cube .swiper-slide-next+.swiper-slide {
  pointer-events: auto;
  visibility: visible
}

.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-top,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-bottom,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-left,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden
}

.swiper.swiper-flip {
  overflow: visible
}

.swiper-flip .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1
}

.swiper-flip .swiper-slide .swiper-slide {
  pointer-events: none
}

.swiper-flip .swiper-slide-active,
.swiper-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto
}

.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-top,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-bottom,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-left,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden
}

.swiper-creative .swiper-slide {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  overflow: hidden;
  transition-property: transform, opacity, height
}

.swiper.swiper-cards {
  overflow: visible
}

.swiper-cards .swiper-slide {
  transform-origin: center bottom;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  overflow: hidden
}

[data-simplebar] {
  position: relative;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start
}

.simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit
}

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0
}

.simplebar-offset {
  direction: inherit !important;
  box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch
}

.simplebar-content-wrapper {
  direction: inherit;
  box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%;
  width: auto;
  max-width: 100%;
  max-height: 100%;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0
}

.simplebar-content:after,
.simplebar-content:before {
  content: " ";
  display: table
}

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none
}

.simplebar-height-auto-observer-wrapper {
  box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  flex-grow: inherit;
  flex-shrink: 0;
  flex-basis: 0
}

.simplebar-height-auto-observer {
  box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1
}

.simplebar-track {
  z-index: 1;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden
}

[data-simplebar].simplebar-dragging,
[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all
}

.simplebar-scrollbar {
  position: absolute;
  left: 0;
  right: 0;
  min-height: 10px
}

.simplebar-scrollbar:before {
  position: absolute;
  content: "";
  background: #000;
  border-radius: 7px;
  left: 2px;
  right: 2px;
  opacity: 0;
  transition: opacity .2s .5s linear
}

.simplebar-scrollbar.simplebar-visible:before {
  opacity: .5;
  transition-delay: 0s;
  transition-duration: 0s
}

.simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px
}

.simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px;
  left: 2px;
  right: 2px
}

.simplebar-track.simplebar-horizontal {
  left: 0;
  height: 11px
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  right: auto;
  left: 0;
  top: 0;
  bottom: 0;
  min-height: 0;
  min-width: 10px;
  width: auto
}

[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {
  right: auto;
  left: 0
}

.simplebar-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
  -ms-overflow-style: scrollbar !important
}

.simplebar-dummy-scrollbar-size>div {
  width: 200%;
  height: 200%;
  margin: 10px 0
}

.simplebar-hide-scrollbar {
  position: fixed;
  left: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none
}

.flatpickr-calendar {
  background: transparent;
  opacity: 0;
  display: none;
  text-align: center;
  visibility: hidden;
  padding: 0;
  -webkit-animation: none;
  animation: none;
  direction: ltr;
  border: 0;
  font-size: 14px;
  line-height: 24px;
  border-radius: 5px;
  position: absolute;
  width: 307.875px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  background: #fff;
  -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, .08);
  box-shadow: 1px 0 #e6e6e6, -1px 0 #e6e6e6, 0 1px #e6e6e6, 0 -1px #e6e6e6, 0 3px 13px #00000014
}

.flatpickr-calendar.open,
.flatpickr-calendar.inline {
  opacity: 1;
  max-height: 640px;
  visibility: visible
}

.flatpickr-calendar.open {
  display: inline-block;
  z-index: 99999
}

.flatpickr-calendar.animate.open {
  -webkit-animation: fpFadeInDown .3s cubic-bezier(.23, 1, .32, 1);
  animation: fpFadeInDown .3s cubic-bezier(.23, 1, .32, 1)
}

.flatpickr-calendar.inline {
  display: block;
  position: relative;
  top: 2px
}

.flatpickr-calendar.static {
  position: absolute;
  top: calc(100% + 2px)
}

.flatpickr-calendar.static.open {
  z-index: 999;
  display: block
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {
  -webkit-box-shadow: none !important;
  box-shadow: none !important
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {
  -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
  box-shadow: -2px 0 #e6e6e6, 5px 0 #e6e6e6
}

.flatpickr-calendar .hasWeeks .dayContainer,
.flatpickr-calendar .hasTime .dayContainer {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0
}

.flatpickr-calendar .hasWeeks .dayContainer {
  border-left: 0
}

.flatpickr-calendar.hasTime .flatpickr-time {
  height: 40px;
  border-top: 1px solid #e6e6e6
}

.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
  height: auto
}

.flatpickr-calendar:before,
.flatpickr-calendar:after {
  position: absolute;
  display: block;
  pointer-events: none;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  left: 22px
}

.flatpickr-calendar.rightMost:before,
.flatpickr-calendar.arrowRight:before,
.flatpickr-calendar.rightMost:after,
.flatpickr-calendar.arrowRight:after {
  left: auto;
  right: 22px
}

.flatpickr-calendar.arrowCenter:before,
.flatpickr-calendar.arrowCenter:after {
  left: 50%;
  right: 50%
}

.flatpickr-calendar:before {
  border-width: 5px;
  margin: 0 -5px
}

.flatpickr-calendar:after {
  border-width: 4px;
  margin: 0 -4px
}

.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  bottom: 100%
}

.flatpickr-calendar.arrowTop:before {
  border-bottom-color: #e6e6e6
}

.flatpickr-calendar.arrowTop:after {
  border-bottom-color: #fff
}

.flatpickr-calendar.arrowBottom:before,
.flatpickr-calendar.arrowBottom:after {
  top: 100%
}

.flatpickr-calendar.arrowBottom:before {
  border-top-color: #e6e6e6
}

.flatpickr-calendar.arrowBottom:after {
  border-top-color: #fff
}

.flatpickr-calendar:focus {
  outline: 0
}

.flatpickr-wrapper {
  position: relative;
  display: inline-block
}

.flatpickr-months {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex
}

.flatpickr-months .flatpickr-month {
  background: transparent;
  color: #000000e6;
  fill: #000000e6;
  height: 34px;
  line-height: 1;
  text-align: center;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow: hidden;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  text-decoration: none;
  cursor: pointer;
  position: absolute;
  top: 0;
  height: 34px;
  padding: 10px;
  z-index: 3;
  color: #000000e6;
  fill: #000000e6
}

.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,
.flatpickr-months .flatpickr-next-month.flatpickr-disabled {
  display: none
}

.flatpickr-months .flatpickr-prev-month i,
.flatpickr-months .flatpickr-next-month i {
  position: relative
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  left: 0
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  right: 0
}

.flatpickr-months .flatpickr-prev-month:hover,
.flatpickr-months .flatpickr-next-month:hover {
  color: #959ea9
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: #f64747
}

.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg {
  width: 14px;
  height: 14px
}

.flatpickr-months .flatpickr-prev-month svg path,
.flatpickr-months .flatpickr-next-month svg path {
  -webkit-transition: fill .1s;
  transition: fill .1s;
  fill: inherit
}

.numInputWrapper {
  position: relative;
  height: auto
}

.numInputWrapper input,
.numInputWrapper span {
  display: inline-block
}

.numInputWrapper input {
  width: 100%
}

.numInputWrapper input::-ms-clear {
  display: none
}

.numInputWrapper input::-webkit-outer-spin-button,
.numInputWrapper input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none
}

.numInputWrapper span {
  position: absolute;
  right: 0;
  width: 14px;
  padding: 0 4px 0 2px;
  height: 50%;
  line-height: 50%;
  opacity: 0;
  cursor: pointer;
  border: 1px solid rgba(57, 57, 57, .15);
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.numInputWrapper span:hover {
  background: #0000001a
}

.numInputWrapper span:active {
  background: #0003
}

.numInputWrapper span:after {
  display: block;
  content: "";
  position: absolute
}

.numInputWrapper span.arrowUp {
  top: 0;
  border-bottom: 0
}

.numInputWrapper span.arrowUp:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(57, 57, 57, .6);
  top: 26%
}

.numInputWrapper span.arrowDown {
  top: 50%
}

.numInputWrapper span.arrowDown:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(57, 57, 57, .6);
  top: 40%
}

.numInputWrapper span svg {
  width: inherit;
  height: auto
}

.numInputWrapper span svg path {
  fill: #00000080
}

.numInputWrapper:hover {
  background: #0000000d
}

.numInputWrapper:hover span {
  opacity: 1
}

.flatpickr-current-month {
  font-size: 135%;
  line-height: inherit;
  font-weight: 300;
  color: inherit;
  position: absolute;
  width: 75%;
  left: 12.5%;
  padding: 7.48px 0 0;
  line-height: 1;
  height: 34px;
  display: inline-block;
  text-align: center;
  -webkit-transform: translate3d(0px, 0px, 0px);
  transform: translateZ(0)
}

.flatpickr-current-month span.cur-month {
  font-family: inherit;
  font-weight: 700;
  color: inherit;
  display: inline-block;
  margin-left: .5ch;
  padding: 0
}

.flatpickr-current-month span.cur-month:hover {
  background: #0000000d
}

.flatpickr-current-month .numInputWrapper {
  width: 6ch;
  width: 7ch�;
  display: inline-block
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
  border-bottom-color: #000000e6
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
  border-top-color: #000000e6
}

.flatpickr-current-month input.cur-year {
  background: transparent;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: inherit;
  cursor: text;
  padding: 0 0 0 .5ch;
  margin: 0;
  display: inline-block;
  font-size: inherit;
  font-family: inherit;
  font-weight: 300;
  line-height: inherit;
  height: auto;
  border: 0;
  border-radius: 0;
  vertical-align: initial;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield
}

.flatpickr-current-month input.cur-year:focus {
  outline: 0
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
  font-size: 100%;
  color: #00000080;
  background: transparent;
  pointer-events: none
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
  appearance: menulist;
  background: transparent;
  border: none;
  border-radius: 0;
  box-sizing: border-box;
  color: inherit;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  font-weight: 300;
  height: auto;
  line-height: inherit;
  margin: -1px 0 0;
  outline: none;
  padding: 0 0 0 .5ch;
  position: relative;
  vertical-align: initial;
  -webkit-box-sizing: border-box;
  -webkit-appearance: menulist;
  -moz-appearance: menulist;
  width: auto
}

.flatpickr-current-month .flatpickr-monthDropdown-months:focus,
.flatpickr-current-month .flatpickr-monthDropdown-months:active {
  outline: none
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background: #0000000d
}

.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  background-color: transparent;
  outline: none;
  padding: 0
}

.flatpickr-weekdays {
  background: transparent;
  text-align: center;
  overflow: hidden;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  height: 28px
}

.flatpickr-weekdays .flatpickr-weekdaycontainer {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1
}

span.flatpickr-weekday {
  cursor: default;
  font-size: 90%;
  background: transparent;
  color: #0000008a;
  line-height: 1;
  margin: 0;
  text-align: center;
  display: block;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-weight: bolder
}

.dayContainer,
.flatpickr-weeks {
  padding: 1px 0 0
}

.flatpickr-days {
  position: relative;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  width: 307.875px
}

.flatpickr-days:focus {
  outline: 0
}

.dayContainer {
  padding: 0;
  outline: 0;
  text-align: left;
  width: 307.875px;
  min-width: 307.875px;
  max-width: 307.875px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-around;
  justify-content: space-around;
  -webkit-transform: translate3d(0px, 0px, 0px);
  transform: translateZ(0);
  opacity: 1
}

.dayContainer+.dayContainer {
  -webkit-box-shadow: -1px 0 0 #e6e6e6;
  box-shadow: -1px 0 #e6e6e6
}

.flatpickr-day {
  background: none;
  border: 1px solid transparent;
  border-radius: 150px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #393939;
  cursor: pointer;
  font-weight: 400;
  width: 14.2857143%;
  -webkit-flex-basis: 14.2857143%;
  -ms-flex-preferred-size: 14.2857143%;
  flex-basis: 14.2857143%;
  max-width: 39px;
  height: 39px;
  line-height: 39px;
  margin: 0;
  display: inline-block;
  position: relative;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  cursor: pointer;
  outline: 0;
  background: #e6e6e6;
  border-color: #e6e6e6
}

.flatpickr-day.today {
  border-color: #959ea9
}

.flatpickr-day.today:hover,
.flatpickr-day.today:focus {
  border-color: #959ea9;
  background: #959ea9;
  color: #fff
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #569ff7;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: #fff;
  border-color: #569ff7
}

.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange,
.flatpickr-day.endRange.startRange {
  border-radius: 50px 0 0 50px
}

.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange,
.flatpickr-day.endRange.endRange {
  border-radius: 0 50px 50px 0
}

.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)) {
  -webkit-box-shadow: -10px 0 0 #569ff7;
  box-shadow: -10px 0 #569ff7
}

.flatpickr-day.selected.startRange.endRange,
.flatpickr-day.startRange.startRange.endRange,
.flatpickr-day.endRange.startRange.endRange {
  border-radius: 50px
}

.flatpickr-day.inRange {
  border-radius: 0;
  -webkit-box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
  box-shadow: -5px 0 #e6e6e6, 5px 0 #e6e6e6
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.notAllowed.nextMonthDay {
  color: #3939394d;
  background: transparent;
  border-color: transparent;
  cursor: default
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
  cursor: not-allowed;
  color: #3939391a
}

.flatpickr-day.week.selected {
  border-radius: 0;
  -webkit-box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;
  box-shadow: -5px 0 #569ff7, 5px 0 #569ff7
}

.flatpickr-day.hidden {
  visibility: hidden
}

.rangeMode .flatpickr-day {
  margin-top: 1px
}

.flatpickr-weekwrapper {
  float: left
}

.flatpickr-weekwrapper .flatpickr-weeks {
  padding: 0 12px;
  -webkit-box-shadow: 1px 0 0 #e6e6e6;
  box-shadow: 1px 0 #e6e6e6
}

.flatpickr-weekwrapper .flatpickr-weekday {
  float: none;
  width: 100%;
  line-height: 28px
}

.flatpickr-weekwrapper span.flatpickr-day,
.flatpickr-weekwrapper span.flatpickr-day:hover {
  display: block;
  width: 100%;
  max-width: none;
  color: #3939394d;
  background: transparent;
  cursor: default;
  border: none
}

.flatpickr-innerContainer {
  display: block;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden
}

.flatpickr-rContainer {
  display: inline-block;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.flatpickr-time {
  text-align: center;
  outline: 0;
  display: block;
  height: 0;
  line-height: 40px;
  max-height: 40px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex
}

.flatpickr-time:after {
  content: "";
  display: table;
  clear: both
}

.flatpickr-time .numInputWrapper {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  width: 40%;
  height: 40px;
  float: left
}

.flatpickr-time .numInputWrapper span.arrowUp:after {
  border-bottom-color: #393939
}

.flatpickr-time .numInputWrapper span.arrowDown:after {
  border-top-color: #393939
}

.flatpickr-time.hasSeconds .numInputWrapper {
  width: 26%
}

.flatpickr-time.time24hr .numInputWrapper {
  width: 49%
}

.flatpickr-time input {
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 0;
  border-radius: 0;
  text-align: center;
  margin: 0;
  padding: 0;
  height: inherit;
  line-height: inherit;
  color: #393939;
  font-size: 14px;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield
}

.flatpickr-time input.flatpickr-hour {
  font-weight: 700
}

.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
  font-weight: 400
}

.flatpickr-time input:focus {
  outline: 0;
  border: 0
}

.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
  height: inherit;
  float: left;
  line-height: inherit;
  color: #393939;
  font-weight: 700;
  width: 2%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center
}

.flatpickr-time .flatpickr-am-pm {
  outline: 0;
  width: 18%;
  cursor: pointer;
  text-align: center;
  font-weight: 400
}

.flatpickr-time input:hover,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time .flatpickr-am-pm:focus {
  background: #eee
}

.flatpickr-input[readonly] {
  cursor: pointer
}

@-webkit-keyframes fpFadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0)
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translateZ(0)
  }
}

@keyframes fpFadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0)
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translateZ(0)
  }
}


/* Customize calendar dropdown */
.react-datepicker {
  font-family: 'Inter', sans-serif;
  /* Replace with your desired font */
  border: 1px solid #d1d5db;
  /* Tailwind's gray-300 */
  background-color: #ffffff;
  color: #1f2937;
  /* Tailwind's gray-800 */
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.react-datepicker__header {
  background-color: #f9fafb;
  /* Tailwind's gray-50 */
  border-bottom: 1px solid #e5e7eb;
  /* gray-200 */
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #111827;
  /* gray-900 */
  font-size: 0.875rem;
  /* text-sm */
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: #3b82f6;
  /* blue-500 */
  color: white;
}


.react-datepicker__day--keyboard-selected:hover {
  color: white;
}
.react-datepicker__day:hover {
  background-color: #e0f2fe;
  /* blue-100 */
  /* color: #1e40af; */
  /* blue-800 */
}

.react-datepicker__close-icon {
  right: 2rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

